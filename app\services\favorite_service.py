"""
收藏功能服务
提供影片收藏的业务逻辑
"""
from typing import List, Optional, Tuple, Dict, Any
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc, asc
from app.models.models import Movie, Favorite, Directory, Genre, Series, Tag, Actor
from app.schemas.schemas import BatchFavoriteRequest, FavoriteFilterParams
import logging

logger = logging.getLogger(__name__)


class FavoriteService:
    """收藏功能服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def add_favorite(self, movie_id: int) -> Tuple[bool, str, Optional[Favorite]]:
        """
        添加收藏
        
        Args:
            movie_id: 影片ID
            
        Returns:
            (是否成功, 消息, 收藏对象)
        """
        try:
            # 检查影片是否存在
            movie = self.db.query(Movie).filter(Movie.id == movie_id).first()
            if not movie:
                return False, "影片不存在", None
            
            # 检查是否已经收藏
            existing_favorite = self.db.query(Favorite).filter(Favorite.movie_id == movie_id).first()
            if existing_favorite:
                return False, "影片已在收藏夹中", existing_favorite
            
            # 创建收藏记录
            favorite = Favorite(movie_id=movie_id)
            self.db.add(favorite)
            self.db.commit()
            self.db.refresh(favorite)
            
            logger.debug(f"成功收藏影片: {movie.title} (ID: {movie_id})")
            return True, "收藏成功", favorite
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"收藏影片时发生错误: {e}")
            return False, f"收藏失败: {str(e)}", None
    
    def remove_favorite(self, movie_id: int) -> Tuple[bool, str]:
        """
        取消收藏
        
        Args:
            movie_id: 影片ID
            
        Returns:
            (是否成功, 消息)
        """
        try:
            # 查找收藏记录
            favorite = self.db.query(Favorite).filter(Favorite.movie_id == movie_id).first()
            if not favorite:
                return False, "影片未收藏"
            
            # 删除收藏记录
            self.db.delete(favorite)
            self.db.commit()
            
            logger.debug(f"成功取消收藏影片 ID: {movie_id}")
            return True, "取消收藏成功"
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"取消收藏时发生错误: {e}")
            return False, f"取消收藏失败: {str(e)}"
    
    def toggle_favorite(self, movie_id: int) -> Tuple[bool, str, bool, Optional[Favorite]]:
        """
        切换收藏状态
        
        Args:
            movie_id: 影片ID
            
        Returns:
            (是否成功, 消息, 是否已收藏, 收藏对象)
        """
        try:
            # 检查当前收藏状态
            favorite = self.db.query(Favorite).filter(Favorite.movie_id == movie_id).first()
            
            if favorite:
                # 已收藏，取消收藏
                success, message = self.remove_favorite(movie_id)
                return success, message, False, None
            else:
                # 未收藏，添加收藏
                success, message, favorite = self.add_favorite(movie_id)
                return success, message, True, favorite
                
        except Exception as e:
            logger.error(f"切换收藏状态时发生错误: {e}")
            return False, f"操作失败: {str(e)}", False, None
    
    def batch_add_favorites(self, movie_ids: List[int]) -> Dict[str, Any]:
        """
        批量添加收藏
        
        Args:
            movie_ids: 影片ID列表
            
        Returns:
            批量操作结果
        """
        try:
            success_count = 0
            failed_count = 0
            success_movie_ids = []
            failed_movie_ids = []
            errors = []
            
            for movie_id in movie_ids:
                success, message, favorite = self.add_favorite(movie_id)
                if success:
                    success_count += 1
                    success_movie_ids.append(movie_id)
                else:
                    failed_count += 1
                    failed_movie_ids.append(movie_id)
                    errors.append(f"影片ID {movie_id}: {message}")
            
            logger.debug(f"批量收藏完成: 成功 {success_count} 个，失败 {failed_count} 个")
            
            return {
                "success_count": success_count,
                "failed_count": failed_count,
                "success_movie_ids": success_movie_ids,
                "failed_movie_ids": failed_movie_ids,
                "errors": errors
            }
            
        except Exception as e:
            logger.error(f"批量收藏时发生错误: {e}")
            return {
                "success_count": 0,
                "failed_count": len(movie_ids),
                "success_movie_ids": [],
                "failed_movie_ids": movie_ids,
                "errors": [f"批量操作失败: {str(e)}"]
            }

    def batch_remove_favorites(self, movie_ids: List[int]) -> Dict[str, Any]:
        """
        批量移除收藏

        Args:
            movie_ids: 影片ID列表

        Returns:
            批量操作结果
        """
        try:
            success_count = 0
            failed_count = 0
            success_movie_ids = []
            failed_movie_ids = []
            errors = []

            for movie_id in movie_ids:
                success, message = self.remove_favorite(movie_id)
                if success:
                    success_count += 1
                    success_movie_ids.append(movie_id)
                else:
                    failed_count += 1
                    failed_movie_ids.append(movie_id)
                    errors.append(f"影片ID {movie_id}: {message}")

            logger.debug(f"批量移除收藏完成: 成功 {success_count} 个，失败 {failed_count} 个")

            return {
                "success_count": success_count,
                "failed_count": failed_count,
                "success_movie_ids": success_movie_ids,
                "failed_movie_ids": failed_movie_ids,
                "errors": errors
            }

        except Exception as e:
            logger.error(f"批量移除收藏时发生错误: {e}")
            return {
                "success_count": 0,
                "failed_count": len(movie_ids),
                "success_movie_ids": [],
                "failed_movie_ids": movie_ids,
                "errors": [f"批量操作失败: {str(e)}"]
            }

    def get_favorite_status(self, movie_id: int) -> Dict[str, Any]:
        """
        获取影片收藏状态
        
        Args:
            movie_id: 影片ID
            
        Returns:
            收藏状态信息
        """
        try:
            favorite = self.db.query(Favorite).filter(Favorite.movie_id == movie_id).first()
            
            if favorite:
                return {
                    "movie_id": movie_id,
                    "is_favorited": True,
                    "favorite_id": favorite.id,
                    "favorited_at": favorite.created_at
                }
            else:
                return {
                    "movie_id": movie_id,
                    "is_favorited": False,
                    "favorite_id": None,
                    "favorited_at": None
                }
                
        except Exception as e:
            logger.error(f"获取收藏状态时发生错误: {e}")
            return {
                "movie_id": movie_id,
                "is_favorited": False,
                "favorite_id": None,
                "favorited_at": None
            }
    
    def get_favorites(self, filter_params: Optional[FavoriteFilterParams] = None) -> Tuple[List[Favorite], int]:
        """
        获取收藏列表（支持过滤和搜索）

        Args:
            filter_params: 过滤参数

        Returns:
            (收藏列表, 总数量)
        """
        try:
            # 如果没有提供过滤参数，使用默认值
            if filter_params is None:
                filter_params = FavoriteFilterParams()

            # 构建基础查询，包含影片信息和关联数据
            query = self.db.query(Favorite).options(
                joinedload(Favorite.movie).joinedload(Movie.genres),
                joinedload(Favorite.movie).joinedload(Movie.series),
                joinedload(Favorite.movie).joinedload(Movie.tags),
                joinedload(Favorite.movie).joinedload(Movie.actors),
                joinedload(Favorite.movie).joinedload(Movie.directory)
            )

            # 只查询启用目录中的影片
            query = query.join(Movie).join(Directory).filter(Directory.enabled == True)

            # 应用搜索过滤
            if filter_params.search:
                search_term = f"%{filter_params.search}%"
                query = query.filter(
                    or_(
                        Movie.title.ilike(search_term),
                        Movie.original_title.ilike(search_term),
                        Movie.plot.ilike(search_term),
                        Movie.actors.any(Actor.name.ilike(search_term))
                    )
                )

            # 应用分类过滤
            if filter_params.genre_ids:
                query = query.filter(Movie.genres.any(Genre.id.in_(filter_params.genre_ids)))

            # 应用系列过滤
            if filter_params.series_ids:
                query = query.filter(Movie.series_id.in_(filter_params.series_ids))

            # 应用标签过滤
            if filter_params.tag_ids:
                query = query.filter(Movie.tags.any(Tag.id.in_(filter_params.tag_ids)))

            # 应用年份范围过滤
            if filter_params.year_from:
                query = query.filter(Movie.year >= filter_params.year_from)
            if filter_params.year_to:
                query = query.filter(Movie.year <= filter_params.year_to)

            # 应用评分范围过滤
            if filter_params.rating_from:
                query = query.filter(Movie.rating >= filter_params.rating_from)
            if filter_params.rating_to:
                query = query.filter(Movie.rating <= filter_params.rating_to)

            # 应用排序
            sort_field = getattr(Favorite, filter_params.sort_by, Favorite.created_at)
            if filter_params.sort_by in ['title', 'year', 'rating']:
                sort_field = getattr(Movie, filter_params.sort_by, Movie.title)

            if filter_params.sort_order == 'desc':
                query = query.order_by(desc(sort_field))
            else:
                query = query.order_by(asc(sort_field))

            # 获取总数（在分页之前）
            total_count = query.count()

            # 应用分页
            favorites = query.offset(filter_params.offset).limit(filter_params.limit).all()

            logger.debug(f"获取收藏列表: {len(favorites)} 个收藏，总计 {total_count} 个")
            return favorites, total_count

        except Exception as e:
            logger.error(f"获取收藏列表时发生错误: {e}")
            return [], 0

    def get_favorites_count(self) -> int:
        """
        获取收藏总数

        Returns:
            收藏总数
        """
        try:
            count = self.db.query(Favorite).count()
            return count
        except Exception as e:
            logger.error(f"获取收藏总数时发生错误: {e}")
            return 0
    

