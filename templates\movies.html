{% extends "base.html" %}

{% block title %}影片库 - 媒体管理器{% endblock %}

{% block page_header %}
<!-- 页面标题区域 -->
<div class="bg-gradient-to-r from-primary/10 to-secondary/10 border-b border-base-300">
    <div class="container mx-auto px-4 py-8">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div>
                <h1 class="text-2xl lg:text-3xl font-bold text-base-content flex items-center gap-2 lg:gap-3">
                    <i class="bi bi-film text-primary text-2xl lg:text-4xl" aria-label="影片库图标"></i>
                    影片库
                </h1>
                <p class="text-base-content/70 mt-1 lg:mt-2 text-sm lg:text-base">浏览和管理您的影片收藏</p>
            </div>
            <div class="flex flex-wrap items-center gap-1 sm:gap-2 lg:gap-3">
                <!-- 主要操作按钮 -->
                <button type="button" class="btn btn-primary btn-sm" id="refresh-movies-btn">
                    <i class="bi bi-arrow-clockwise" aria-label="刷新图标"></i>
                    <span class="hidden sm:inline ml-1">刷新</span>
                </button>
                <button type="button" class="btn btn-outline btn-sm" id="view-toggle-btn" title="切换视图">
                    <i class="bi bi-grid" aria-label="视图切换图标"></i>
                    <span class="hidden lg:inline ml-1">视图</span>
                </button>

                <!-- 筛选操作下拉菜单 -->
                <div class="dropdown dropdown-bottom sm:dropdown-end">
                    <div tabindex="0" role="button" class="btn btn-outline btn-sm" title="筛选选项">
                        <i class="bi bi-funnel text-xs" aria-label="筛选图标"></i>
                        <span class="hidden sm:inline ml-1">筛选</span>
                        <i class="bi bi-chevron-down ml-1 text-xs" aria-label="下拉图标"></i>
                    </div>
                    <ul tabindex="0"
                        class="dropdown-content z-[1] menu p-2 shadow-lg bg-base-100 backdrop-blur-sm rounded-box w-48 sm:w-52 border border-base-300 text-sm sm:right-0">
                        <li class="menu-title">
                            <span class="text-xs font-medium text-base-content/60">更新状态</span>
                        </li>
                        <li><a href="#" id="filter-all-menu" data-filter="all" class="flex items-center gap-2 py-2">
                                <i class="bi bi-list text-xs" aria-label="全部图标"></i>
                                全部影片
                            </a></li>
                        <li><a href="#" id="filter-updated-menu" data-filter="updated" class="flex items-center gap-2 py-2">
                                <i class="bi bi-arrow-clockwise text-success text-xs" aria-label="已更新图标"></i>
                                已更新影片
                            </a></li>
                        <li><a href="#" id="filter-not-updated-menu" data-filter="not-updated" class="flex items-center gap-2 py-2">
                                <i class="bi bi-clock text-warning text-xs" aria-label="未更新图标"></i>
                                未更新影片
                            </a></li>
                        <li><hr class="my-1"></li>
                        <li><a href="#" id="advanced-search-menu-btn" class="flex items-center gap-2 py-2">
                                <i class="bi bi-search-heart text-xs" aria-label="高级搜索图标"></i>
                                高级搜索
                            </a></li>
                        <li><a href="#" id="clear-filters-menu-btn" class="flex items-center gap-2 py-2 text-error">
                                <i class="bi bi-x-circle text-xs" aria-label="清除筛选图标"></i>
                                清除筛选
                            </a></li>
                    </ul>
                </div>

                <!-- 排序下拉菜单 -->
                <div class="dropdown dropdown-bottom dropdown-end">
                    <div tabindex="0" role="button" class="btn btn-outline btn-sm" title="排序">
                        <i class="bi bi-sort-down text-xs" aria-label="排序图标"></i>
                        <span class="hidden sm:inline ml-1">排序</span>
                        <i class="bi bi-chevron-down ml-1 text-xs" aria-label="下拉图标"></i>
                    </div>
                    <ul tabindex="0"
                        class="dropdown-content z-[1] menu p-2 shadow-lg bg-base-100 backdrop-blur-sm rounded-box w-44 sm:w-48 border border-base-300 text-sm right-0">
                        <li><a href="#" data-sort="title" class="flex items-center gap-2 py-2">
                                <i class="bi bi-sort-alpha-down text-xs" aria-label="标题排序图标"></i>
                                按标题排序
                            </a></li>
                        <li><a href="#" data-sort="year" class="flex items-center gap-2 py-2">
                                <i class="bi bi-calendar text-xs" aria-label="年份排序图标"></i>
                                按年份排序
                            </a></li>
                        <li><a href="#" data-sort="rating" class="flex items-center gap-2 py-2">
                                <i class="bi bi-star-fill text-xs" aria-label="评分排序图标"></i>
                                按评分排序
                            </a></li>
                        <li><a href="#" data-sort="created_at" class="flex items-center gap-2 py-2">
                                <i class="bi bi-clock text-xs" aria-label="时间排序图标"></i>
                                按添加时间排序
                            </a></li>
                    </ul>
                </div>

                <!-- 批量选择按钮 -->
                <button class="btn btn-outline btn-sm" type="button" id="batch-select-btn" title="批量选择影片">
                    <i class="bi bi-ui-checks text-xs" aria-label="批量选择图标"></i>
                    <span class="hidden sm:inline ml-1">批量选择</span>
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- Directory Filter Info -->
<div class="mb-4 hidden" id="directory-filter-info">
    <div class="alert alert-info">
        <div class="flex items-center gap-3">
            <i class="bi bi-folder" aria-label="目录图标"></i>
            <div class="flex-1">
                <span class="font-medium">当前筛选目录：</span>
                <span id="current-directory-name" class="font-semibold"></span>
            </div>
            <div class="flex gap-2">
                <button class="btn btn-outline btn-sm" id="show-all-directories-btn">
                    <i class="bi bi-list" aria-label="列表图标"></i>
                    显示所有目录
                </button>
                <button class="btn btn-ghost btn-sm" id="clear-directory-filter-btn">
                    <i class="bi bi-x" aria-label="清除图标"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="card bg-base-100 shadow-lg border border-base-300 mb-3 md:mb-4">
    <div class="card-body p-3 md:p-4">
        <!-- 主搜索区域 -->
        <div class="flex flex-col md:flex-row gap-2 md:gap-3 items-stretch md:items-end mb-3 md:mb-4">
            <!-- 搜索输入框 -->
            <div class="form-control flex-1">
                <label class="label py-1">
                    <span class="label-text font-medium text-sm">搜索影片</span>
                    <span class="label-text-alt text-xs text-base-content/60 hidden sm:inline">支持标题、演员、导演搜索</span>
                </label>
                <div class="join w-full">
                    <div class="relative flex-1">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="bi bi-search text-base-content/40" aria-label="搜索图标"></i>
                        </div>
                        <input type="text" class="input input-bordered w-full pl-9 pr-10 join-item focus:input-primary input-sm md:input-md"
                            id="search-input" placeholder="输入影片名称、演员或导演..." autocomplete="off">
                        <button
                            class="absolute inset-y-0 right-0 pr-2 flex items-center btn btn-ghost btn-xs btn-circle hidden"
                            type="button" id="clear-search-btn" title="清除搜索">
                            <i class="bi bi-x" style="font-size: 0.75rem;" aria-label="清除搜索图标"></i>
                        </button>
                    </div>
                    <button class="btn btn-primary join-item btn-sm md:btn-md" type="button" id="search-btn">
                        <i class="bi bi-search" aria-label="搜索图标"></i>
                        <span class="hidden sm:inline ml-1">搜索</span>
                    </button>
                </div>
            </div>

            <!-- 当前筛选状态显示 -->
            <div class="form-control w-full md:w-auto">
                <label class="label py-1 md:opacity-0">
                    <span class="label-text text-sm">　</span>
                </label>
                <div class="flex items-center gap-2">
                    <!-- 当前筛选状态指示器 -->
                    <div class="badge badge-outline h-8 md:h-10 px-3 flex items-center" id="current-filter-badge">
                        <i class="bi bi-list mr-1 text-xs" aria-label="筛选状态图标"></i>
                        <span id="current-filter-text" class="text-xs md:text-sm">全部</span>
                    </div>
                    <!-- 批量NFO同步按钮 - 仅在筛选已更新影片时显示 -->
                    <button class="btn btn-primary btn-sm md:btn-md hidden" type="button" id="batch-nfo-sync-btn" title="批量同步已更新影片的NFO文件">
                        <i class="bi bi-cloud-upload text-xs" aria-label="批量同步NFO图标"></i>
                        <span class="hidden sm:inline ml-1 text-xs md:text-sm">同步NFO</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Movies Grid -->
<div class="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-4"
    id="movies-container">
    <!-- Movies will be loaded here -->
</div>

<!-- Movies List Table (hidden by default) -->
<div class="overflow-x-auto hidden" id="movies-list-container">
    <table class="table table-zebra w-full">
        <thead>
            <tr>
                <th class="w-16 sm:w-20">封面</th>
                <th class="cursor-pointer hover:bg-base-200 transition-colors min-w-32" data-sort="title">
                    <div class="flex items-center gap-2">
                        <span>标题</span>
                        <i class="bi bi-arrow-down-up text-xs opacity-50"></i>
                    </div>
                </th>
                <th class="cursor-pointer hover:bg-base-200 transition-colors w-16 sm:w-20 hidden sm:table-cell" data-sort="rating">
                    <div class="flex items-center gap-2">
                        <span>评分</span>
                        <i class="bi bi-arrow-down-up text-xs opacity-50"></i>
                    </div>
                </th>
                <th class="cursor-pointer hover:bg-base-200 transition-colors w-24 sm:w-32 hidden md:table-cell" data-sort="series">
                    <div class="flex items-center gap-2">
                        <span>系列</span>
                        <i class="bi bi-arrow-down-up text-xs opacity-50"></i>
                    </div>
                </th>
                <th class="cursor-pointer hover:bg-base-200 transition-colors w-16 sm:w-24 hidden lg:table-cell" data-sort="year">
                    <div class="flex items-center gap-2">
                        <span>年份</span>
                        <i class="bi bi-arrow-down-up text-xs opacity-50"></i>
                    </div>
                </th>
                <th class="cursor-pointer hover:bg-base-200 transition-colors w-16 sm:w-24 hidden lg:table-cell" data-sort="runtime">
                    <div class="flex items-center gap-2">
                        <span>时长</span>
                        <i class="bi bi-arrow-down-up text-xs opacity-50"></i>
                    </div>
                </th>
                <th class="w-16 sm:w-20">操作</th>
            </tr>
        </thead>
        <tbody id="movies-list-body">
            <!-- List items will be loaded here -->
        </tbody>
    </table>
</div>

<!-- Loading Skeleton -->
<div class="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-4 hidden"
    id="loading-skeleton">
    <!-- Skeleton cards will be generated here -->
</div>

<!-- Loading Skeleton for List View -->
<div class="overflow-x-auto hidden" id="loading-skeleton-list">
    <table class="table table-zebra w-full">
        <thead>
            <tr>
                <th class="w-16 sm:w-20">封面</th>
                <th class="min-w-32">标题</th>
                <th class="w-16 sm:w-20 hidden sm:table-cell">评分</th>
                <th class="w-24 sm:w-32 hidden md:table-cell">系列</th>
                <th class="w-16 sm:w-24 hidden lg:table-cell">年份</th>
                <th class="w-16 sm:w-24 hidden lg:table-cell">时长</th>
                <th class="w-16 sm:w-20">操作</th>
            </tr>
        </thead>
        <tbody>
            <!-- Generate skeleton rows -->
            <tr class="animate-pulse">
                <td><div class="skeleton w-10 sm:w-12 h-14 sm:h-16 rounded"></div></td>
                <td>
                    <div class="skeleton h-4 w-full mb-2"></div>
                    <div class="skeleton h-3 w-3/4 sm:hidden"></div>
                </td>
                <td class="hidden sm:table-cell"><div class="skeleton h-4 w-12"></div></td>
                <td class="hidden md:table-cell"><div class="skeleton h-4 w-20"></div></td>
                <td class="hidden lg:table-cell"><div class="skeleton h-4 w-16"></div></td>
                <td class="hidden lg:table-cell"><div class="skeleton h-4 w-16"></div></td>
                <td><div class="skeleton h-6 sm:h-8 w-6 sm:w-8 rounded-full"></div></td>
            </tr>
            <tr class="animate-pulse">
                <td><div class="skeleton w-10 sm:w-12 h-14 sm:h-16 rounded"></div></td>
                <td>
                    <div class="skeleton h-4 w-full mb-2"></div>
                    <div class="skeleton h-3 w-3/4 sm:hidden"></div>
                </td>
                <td class="hidden sm:table-cell"><div class="skeleton h-4 w-12"></div></td>
                <td class="hidden md:table-cell"><div class="skeleton h-4 w-20"></div></td>
                <td class="hidden lg:table-cell"><div class="skeleton h-4 w-16"></div></td>
                <td class="hidden lg:table-cell"><div class="skeleton h-4 w-16"></div></td>
                <td><div class="skeleton h-6 sm:h-8 w-6 sm:w-8 rounded-full"></div></td>
            </tr>
            <tr class="animate-pulse">
                <td><div class="skeleton w-10 sm:w-12 h-14 sm:h-16 rounded"></div></td>
                <td>
                    <div class="skeleton h-4 w-full mb-2"></div>
                    <div class="skeleton h-3 w-3/4 sm:hidden"></div>
                </td>
                <td class="hidden sm:table-cell"><div class="skeleton h-4 w-12"></div></td>
                <td class="hidden md:table-cell"><div class="skeleton h-4 w-20"></div></td>
                <td class="hidden lg:table-cell"><div class="skeleton h-4 w-16"></div></td>
                <td class="hidden lg:table-cell"><div class="skeleton h-4 w-16"></div></td>
                <td><div class="skeleton h-6 sm:h-8 w-6 sm:w-8 rounded-full"></div></td>
            </tr>
        </tbody>
    </table>
</div>

<!-- Empty State -->
<div class="flex flex-col items-center justify-center py-16 hidden" id="empty-state">
    <div class="bg-base-200 rounded-full p-6 mb-6">
        <i class="bi bi-film text-base-content/40" style="font-size: 4rem;" aria-label="空影片图标"></i>
    </div>
    <h3 class="text-2xl font-bold mb-2 text-base-content">没有找到影片</h3>
    <p class="text-base-content/60 mb-6 text-center max-w-md">尝试调整搜索条件或清除筛选器来查看更多影片</p>
    <button type="button" class="btn btn-primary btn-wide" id="empty-clear-filters-btn">
        <i class="bi bi-arrow-clockwise mr-2" aria-label="清除筛选图标"></i>
        清除筛选器
    </button>
</div>

<!-- Error State -->
<div class="flex flex-col items-center justify-center py-16 hidden" id="error-state">
    <div class="bg-error/10 rounded-full p-6 mb-6">
        <i class="bi bi-exclamation-triangle text-error" style="font-size: 4rem;" aria-label="错误图标"></i>
    </div>
    <h3 class="text-2xl font-bold mb-2 text-base-content">加载失败</h3>
    <p class="text-base-content/60 mb-6 text-center max-w-md" id="error-message">无法加载影片数据，请稍后重试</p>
    <button type="button" class="btn btn-error btn-wide" id="error-retry-btn">
        <i class="bi bi-arrow-clockwise mr-2" aria-label="重试图标"></i>
        重试
    </button>
</div>

<!-- Pagination -->
<div class="flex flex-col md:flex-row justify-between items-center mt-8 gap-4" id="pagination-container">
    <div class="text-base-content/70" id="pagination-info">
        显示 0 - 0 条，共 0 条记录
    </div>
    <div class="join" id="pagination-controls">
        <!-- Pagination controls will be generated here -->
    </div>
</div>

<!-- Load More Button (for infinite scroll alternative) -->
<div class="flex justify-center mt-8 hidden" id="load-more-container">
    <button type="button" class="btn btn-outline btn-primary btn-wide" id="load-more-btn">
        <span class="loading loading-spinner loading-sm mr-2 hidden"></span>
        <i class="bi bi-plus mr-2" aria-label="加载更多图标"></i>
        加载更多
    </button>
</div>

<!-- Advanced Search Modal -->
<dialog id="advanced-search-modal" class="modal bg-base-100/80 overflow-visible">
    <div class="modal-box w-11/12 max-w-4xl bg-base-100/80 backdrop-blur-sm overflow-visible">
        <form method="dialog">
            <button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2">✕</button>
        </form>
        <h3 class="font-bold text-xl mb-6">高级搜索</h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 overflow-visible">
            <!-- 标签选择 -->
            <div class="form-control overflow-visible">
                <label class="label">
                    <span class="label-text font-medium flex items-center gap-2">
                        <i class="bi bi-tags text-primary" aria-label="标签图标"></i>
                        标签
                    </span>
                </label>
                <div id="tags-selector" class="overflow-visible"></div>
            </div>

            <!-- 分类选择 -->
            <div class="form-control overflow-visible">
                <label class="label">
                    <span class="label-text font-medium flex items-center gap-2">
                        <i class="bi bi-collection text-secondary" aria-label="分类图标"></i>
                        分类
                    </span>
                </label>
                <div id="genres-selector" class="overflow-visible"></div>
            </div>

            <!-- 系列选择 -->
            <div class="form-control overflow-visible">
                <label class="label">
                    <span class="label-text font-medium flex items-center gap-2">
                        <i class="bi bi-collection-play text-accent" aria-label="系列图标"></i>
                        系列
                    </span>
                </label>
                <div id="series-selector" class="overflow-visible"></div>
            </div>

            <!-- 年份和评分 -->
            <div class="space-y-4">
                <!-- 年份选择 -->
                <div class="form-control">
                    <label class="label">
                        <span class="label-text font-medium flex items-center gap-2">
                            <i class="bi bi-calendar text-warning" aria-label="日历图标"></i>
                            年份范围
                        </span>
                    </label>
                    <div class="grid grid-cols-2 gap-2">
                        <input type="number" class="input input-bordered input-sm" id="year-from" placeholder="起始年份"
                            min="1900" max="2100" step="1">
                        <input type="number" class="input input-bordered input-sm" id="year-to" placeholder="结束年份"
                            min="1900" max="2100" step="1">
                    </div>
                </div>

                <!-- 评分选择 -->
                <div class="form-control">
                    <label class="label">
                        <span class="label-text font-medium flex items-center gap-2">
                            <i class="bi bi-star-fill text-error" aria-label="星星图标"></i>
                            评分范围
                        </span>
                    </label>
                    <div class="grid grid-cols-2 gap-2">
                        <select class="select select-bordered select-sm" id="rating-from">
                            <option value="">最低评分</option>
                            <option value="1">1分以上</option>
                            <option value="2">2分以上</option>
                            <option value="3">3分以上</option>
                            <option value="4">4分以上</option>
                            <option value="5">5分以上</option>
                            <option value="6">6分以上</option>
                            <option value="7">7分以上</option>
                            <option value="8">8分以上</option>
                            <option value="9">9分以上</option>
                        </select>
                        <select class="select select-bordered select-sm" id="rating-to">
                            <option value="">最高评分</option>
                            <option value="10">10分以下</option>
                            <option value="9">9分以下</option>
                            <option value="8">8分以下</option>
                            <option value="7">7分以下</option>
                            <option value="6">6分以下</option>
                            <option value="5">5分以下</option>
                            <option value="4">4分以下</option>
                            <option value="3">3分以下</option>
                            <option value="2">2分以下</option>
                        </select>
                    </div>
                </div>

                <!-- 已更新状态选择 -->
                <div class="form-control">
                    <label class="label">
                        <span class="label-text font-medium flex items-center gap-2">
                            <i class="bi bi-arrow-clockwise text-info" aria-label="更新图标"></i>
                            更新状态
                        </span>
                    </label>
                    <select class="select select-bordered select-sm" id="is-updated">
                        <option value="">全部</option>
                        <option value="true">已更新</option>
                        <option value="false">未更新</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="modal-action mt-8">
            <button type="button" class="btn btn-outline" id="reset-advanced-search">
                <i class="bi bi-arrow-clockwise mr-2" aria-label="重置图标"></i>
                重置
            </button>
            <button type="button" class="btn btn-ghost"
                onclick="document.getElementById('advanced-search-modal').close()">取消</button>
            <button type="button" class="btn btn-primary" id="apply-advanced-search">
                <i class="bi bi-funnel mr-2" aria-label="筛选图标"></i>
                应用筛选
            </button>
        </div>
    </div>
</dialog>

<!-- 批量NFO同步确认对话框 -->
<dialog id="batch-nfo-sync-confirm-modal" class="modal">
    <div class="modal-box">
        <h3 class="font-bold text-lg mb-4">
            <i class="bi bi-cloud-upload text-primary mr-2" aria-label="批量同步NFO图标"></i>
            批量同步NFO文件
        </h3>

        <div class="py-4">
            <div class="alert alert-info mb-4">
                <i class="bi bi-info-circle" aria-label="信息图标"></i>
                <div>
                    <div class="font-medium">即将同步 <span id="batch-nfo-count" class="font-bold text-primary">0</span> 部已更新影片的NFO文件</div>
                    <div class="text-sm mt-1">同步成功后将自动清理影片的已更新标记</div>
                </div>
            </div>

            <p class="text-sm text-base-content/70">
                此操作将把数据库中的影片信息同步到对应的NFO文件中，包括标题、剧情、演员、分类等信息。
            </p>
        </div>

        <div class="modal-action">
            <button type="button" class="btn btn-ghost" onclick="document.getElementById('batch-nfo-sync-confirm-modal').close()">
                取消
            </button>
            <button type="button" class="btn btn-primary" id="confirm-batch-nfo-sync">
                <i class="bi bi-cloud-upload mr-2" aria-label="同步图标"></i>
                开始同步
            </button>
        </div>
    </div>
</dialog>

<!-- 批量NFO同步进度对话框 -->
<dialog id="batch-nfo-sync-progress-modal" class="modal">
    <div class="modal-box">
        <h3 class="font-bold text-lg mb-4">
            <i class="bi bi-cloud-upload text-primary mr-2" aria-label="同步进度图标"></i>
            正在同步NFO文件
        </h3>

        <div class="py-4">
            <div class="mb-4">
                <div class="flex justify-between text-sm mb-2">
                    <span>同步进度</span>
                    <span id="batch-nfo-progress-text">0 / 0</span>
                </div>
                <progress class="progress progress-primary w-full" id="batch-nfo-progress" value="0" max="100"></progress>
            </div>

            <div class="text-sm text-base-content/70">
                <div id="batch-nfo-current-movie">准备开始同步...</div>
            </div>
        </div>

        <div class="modal-action">
            <button type="button" class="btn btn-outline" id="cancel-batch-nfo-sync">取消同步</button>
        </div>
    </div>
</dialog>

<!-- 批量NFO同步结果对话框 -->
<dialog id="batch-nfo-sync-result-modal" class="modal">
    <div class="modal-box">
        <h3 class="font-bold text-lg mb-4">
            <i class="bi bi-check-circle text-success mr-2" aria-label="同步结果图标"></i>
            批量同步完成
        </h3>

        <div class="py-4" id="batch-nfo-result-content">
            <!-- 结果内容将通过JavaScript动态填充 -->
        </div>

        <div class="modal-action">
            <button type="button" class="btn btn-primary" onclick="document.getElementById('batch-nfo-sync-result-modal').close()">
                确定
            </button>
        </div>
    </div>
</dialog>

<!-- 隐藏的原始按钮元素，保持JavaScript兼容性 -->
<div class="hidden">
    <button type="button" id="clear-filters-btn"></button>
    <button type="button" id="advanced-search-btn"></button>
    <button type="button" id="filter-all" data-filter="all"></button>
    <button type="button" id="filter-updated" data-filter="updated"></button>
    <button type="button" id="filter-not-updated" data-filter="not-updated"></button>
</div>

{% endblock %}

{% block extra_js %}
<script src="/static/js/multi-select-dropdown.js?v={{ app_version }}"></script>
<script src="/static/js/advanced-search.js?v={{ app_version }}"></script>
<script src="/static/js/favorite-manager.js?v={{ app_version }}"></script>
<script src="/static/js/batch-toolbar.js?v={{ app_version }}"></script>
<script src="/static/js/movies.js?v={{ app_version }}"></script>
{% endblock %}