/**
 * 影片库页面脚本
 * 处理影片列表的加载、搜索、过滤和分页
 */

class MoviesPage {
    constructor() {
        this.movies = [];
        this.totalCount = 0;
        this.currentPage = 1;
        this.pageSize = 24;
        this.isLoading = false;
        this.searchTimeout = null;

        // 排序参数
        this.sortBy = 'title';
        this.sortOrder = 'asc';

        // 视图模式管理
        this.viewMode = 'grid'; // 'grid' 或 'list'
        this.VIEW_MODE_STORAGE_KEY = 'movies_view_mode';

        // 过滤参数
        this.filters = {
            search: '',
            tag_ids: [],
            genre_ids: [],
            series_ids: [],
            actor_ids: [],
            year_from: '',
            year_to: '',
            rating_from: '',
            rating_to: '',
            directory_id: null, // 新增目录ID筛选
            is_updated: null // 新增已更新状态筛选
        };

        // 缓存的选项数据
        this.genres = [];
        this.series = [];
        this.years = [];

        // 批量工具栏
        this.batchToolbar = null;
    }

    /**
     * 初始化影片库页面
     */
    async init() {
        this.initViewMode(); // 初始化视图模式
        this.bindEvents();
        this.initBatchToolbar();
        this.initFavoriteStatusListener();
        await this.loadFilterOptions();
        this.initializeFromUrl(); // 先初始化URL参数，再加载影片
        await this.loadMovies();
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 搜索输入框
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            searchInput.addEventListener('input', utils.debounce((e) => {
                this.filters.search = e.target.value.trim();
                this.currentPage = 1;
                this.loadMovies();
                this.updateUrl();
            }, 500));
        }

        // 清除搜索按钮
        const clearSearchBtn = document.getElementById('clear-search-btn');
        if (clearSearchBtn) {
            clearSearchBtn.addEventListener('click', () => {
                searchInput.value = '';
                this.filters.search = '';
                this.currentPage = 1;
                this.loadMovies();
                this.updateUrl();
                clearSearchBtn.classList.add('hidden');
            });
        }

        // 过滤器
        this.bindFilterEvents();

        // 批量选择按钮
        const batchSelectBtn = document.getElementById('batch-select-btn');
        if (batchSelectBtn) {
            batchSelectBtn.addEventListener('click', () => {
                this.toggleBatchMode();
            });
        }

        // 刷新按钮
        const refreshBtn = document.getElementById('refresh-movies-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.loadMovies();
            });
        }

        // 视图切换按钮
        const viewToggleBtn = document.getElementById('view-toggle-btn');
        if (viewToggleBtn) {
            viewToggleBtn.addEventListener('click', () => {
                this.toggleViewMode();
            });
        }

        // 清除筛选按钮
        const clearFiltersBtn = document.getElementById('clear-filters-btn');
        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', () => {
                this.clearFilters();
            });
        }

        // 空状态清除筛选按钮
        const emptyClearFiltersBtn = document.getElementById('empty-clear-filters-btn');
        if (emptyClearFiltersBtn) {
            emptyClearFiltersBtn.addEventListener('click', () => {
                this.clearFilters();
            });
        }

        // 错误重试按钮
        const errorRetryBtn = document.getElementById('error-retry-btn');
        if (errorRetryBtn) {
            errorRetryBtn.addEventListener('click', () => {
                this.loadMovies();
            });
        }

        // 批量选择复选框点击事件（阻止事件冒泡）
        document.addEventListener('click', (e) => {
            if (e.target.matches('.batch-select-checkbox')) {
                e.stopPropagation(); // 阻止事件冒泡到卡片点击事件
                return;
            }
        });

        // 影片卡片和列表项点击事件
        document.addEventListener('click', (e) => {
            // 检查是否点击了按钮、链接、复选框或其他交互元素，如果是则不处理卡片点击
            if (e.target.closest('button') ||
                e.target.closest('a') ||
                e.target.closest('.btn') ||
                e.target.closest('input[type="checkbox"]') ||
                e.target.closest('.batch-select-container') ||
                e.target.closest('.favorite-btn-container') ||
                e.target.closest('th[data-sort]')) { // 排除表头排序点击
                return;
            }

            // 处理网格视图的卡片点击
            const movieCard = e.target.closest('.movie-card');
            if (movieCard) {
                const movieId = movieCard.getAttribute('data-movie-id');
                if (movieId) {
                    this.showMovieDetail(movieId);
                }
                return;
            }

            // 处理列表视图的行点击
            const movieListItem = e.target.closest('.movie-list-item');
            if (movieListItem) {
                const movieId = movieListItem.getAttribute('data-movie-id');
                if (movieId) {
                    this.showMovieDetail(movieId);
                }
            }
        });

        // 排序功能事件绑定
        document.addEventListener('click', (e) => {
            const sortOption = e.target.closest('[data-sort]');
            if (sortOption) {
                e.preventDefault();
                const sortBy = sortOption.getAttribute('data-sort');
                this.setSortOrder(sortBy);
            }
        });

        // 高级搜索事件绑定
        document.addEventListener('advancedSearchApplied', (e) => {
            this.applyAdvancedFilters(e.detail);
        });

        // 搜索按钮事件
        const searchBtn = document.getElementById('search-btn');
        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                this.performSearch();
            });
        }

        // 目录筛选相关事件
        const showAllDirectoriesBtn = document.getElementById('show-all-directories-btn');
        if (showAllDirectoriesBtn) {
            showAllDirectoriesBtn.addEventListener('click', () => {
                this.clearDirectoryFilter();
            });
        }

        const clearDirectoryFilterBtn = document.getElementById('clear-directory-filter-btn');
        if (clearDirectoryFilterBtn) {
            clearDirectoryFilterBtn.addEventListener('click', () => {
                this.clearDirectoryFilter();
            });
        }

        // 更新状态快速筛选按钮（包括菜单中的按钮）
        const filterButtons = document.querySelectorAll('[data-filter]');
        filterButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const filterType = e.target.closest('[data-filter]').getAttribute('data-filter');
                this.setUpdatedFilter(filterType);
            });
        });

        // 批量NFO同步按钮
        const batchNfoSyncBtn = document.getElementById('batch-nfo-sync-btn');
        if (batchNfoSyncBtn) {
            batchNfoSyncBtn.addEventListener('click', () => {
                this.handleBatchNfoSync();
            });
        }

        // 批量NFO同步确认按钮
        const confirmBatchNfoSyncBtn = document.getElementById('confirm-batch-nfo-sync');
        if (confirmBatchNfoSyncBtn) {
            confirmBatchNfoSyncBtn.addEventListener('click', () => {
                this.executeBatchNfoSync();
            });
        }

        // 取消批量NFO同步按钮
        const cancelBatchNfoSyncBtn = document.getElementById('cancel-batch-nfo-sync');
        if (cancelBatchNfoSyncBtn) {
            cancelBatchNfoSyncBtn.addEventListener('click', () => {
                this.cancelBatchNfoSync();
            });
        }

        // 菜单中的按钮事件绑定
        this.bindMenuEvents();
    }

    /**
     * 绑定菜单中的按钮事件
     */
    bindMenuEvents() {
        // 菜单中的高级搜索按钮
        const advancedSearchMenuBtn = document.getElementById('advanced-search-menu-btn');
        if (advancedSearchMenuBtn) {
            advancedSearchMenuBtn.addEventListener('click', (e) => {
                e.preventDefault();
                // 触发原始高级搜索按钮的点击事件
                const originalBtn = document.getElementById('advanced-search-btn');
                if (originalBtn) {
                    originalBtn.click();
                }
            });
        }

        // 菜单中的清除筛选按钮
        const clearFiltersMenuBtn = document.getElementById('clear-filters-menu-btn');
        if (clearFiltersMenuBtn) {
            clearFiltersMenuBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.clearFilters();
            });
        }
    }

    /**
     * 绑定过滤器事件
     */
    bindFilterEvents() {
        // 过滤器事件现在由高级搜索模块处理
    }

    /**
     * 加载过滤器选项
     */
    async loadFilterOptions() {
        try {
            // 并行加载所有选项
            const [genresResponse, seriesResponse] = await Promise.all([
                api.getGenres(),
                api.getSeries()
            ]);

            this.genres = genresResponse.data || [];
            this.series = seriesResponse.data || [];

            this.populateFilterOptions();
        } catch (error) {
            console.error('加载过滤器选项失败:', error);
            const errorMessage = handleApiError(error, '加载过滤器选项', false);
            toast.warning(errorMessage);
        }
    }

    /**
     * 填充过滤器选项
     */
    populateFilterOptions() {
        // 过滤器选项现在由高级搜索模块处理
    }

    /**
     * 填充年份选项
     */
    populateYearOptions() {
        // 年份选项现在由高级搜索模块处理
    }

    /**
     * 加载影片列表
     */
    async loadMovies() {
        if (this.isLoading) return;

        this.isLoading = true;
        this.showLoading();
        this.hideStates();

        try {
            const params = {
                limit: this.pageSize,
                offset: (this.currentPage - 1) * this.pageSize,
                sort_by: this.sortBy,
                sort_order: this.sortOrder,
                ...this.filters
            };

            // 移除空值参数，但保留空数组（后端可以处理）
            Object.keys(params).forEach(key => {
                if (params[key] === '' || params[key] === null || params[key] === undefined) {
                    delete params[key];
                }
                // 移除空数组
                if (Array.isArray(params[key]) && params[key].length === 0) {
                    delete params[key];
                }
            });

            const response = await api.getMovies(params);

            this.movies = response.data || [];
            this.totalCount = response.total_count || 0;

            this.updateMoviesDisplay();
            this.updatePagination();
            this.updateFilterInfo();

        } catch (error) {
            console.error('加载影片列表失败:', error);
            // 使用Toast显示错误，同时禁用handleApiError的自动Toast以避免重复
            const errorMessage = handleApiError(error, '加载影片列表', false);
            toast.error(errorMessage);
            this.showErrorState(errorMessage);
        } finally {
            this.isLoading = false;
            this.hideLoading();
        }
    }

    /**
     * 更新影片显示
     */
    updateMoviesDisplay() {
        if (this.movies.length === 0) {
            this.showEmptyState();
            return;
        }

        if (this.viewMode === 'grid') {
            this.updateGridDisplay();
        } else {
            this.updateListDisplay();
        }

        // 异步更新收藏状态
        this.updateMoviesDisplayWithFavorites();
    }

    /**
     * 更新网格视图显示
     */
    updateGridDisplay() {
        const container = document.getElementById('movies-container');
        if (!container) return;

        const moviesHtml = this.movies.map(movie => this.createMovieCard(movie)).join('');
        container.innerHTML = moviesHtml;

        // 启用图片懒加载
        utils.lazyLoadImages(container);

        // 添加淡入动画
        container.classList.add('fade-in');
    }

    /**
     * 更新列表视图显示
     */
    updateListDisplay() {
        const listBody = document.getElementById('movies-list-body');
        if (!listBody) return;

        const moviesHtml = this.movies.map(movie => this.createMovieListItem(movie)).join('');
        listBody.innerHTML = moviesHtml;

        // 启用图片懒加载
        utils.lazyLoadImages(listBody);

        // 添加淡入动画
        listBody.classList.add('fade-in');
    }

    /**
     * 获取影片图片URL，按优先级选择
     * @param {Object} movie - 影片对象
     * @returns {string|null} 图片URL或null
     */
    getMovieImageUrl(movie) {
        // 优先级：poster_uuid > fanart_uuid > thumb_uuid
        if (movie.poster_uuid) {
            return api.getImageUrl(movie.poster_uuid);
        }
        if (movie.fanart_uuid) {
            return api.getImageUrl(movie.fanart_uuid);
        }
        if (movie.thumb_uuid) {
            return api.getImageUrl(movie.thumb_uuid);
        }
        return null;
    }

    /**
     * 创建影片列表项（表格行）
     */
    createMovieListItem(movie) {
        // 构建图片URL
        let imageUrl = '';
        if (movie.poster_uuid) {
            imageUrl = `/api/images/${movie.poster_uuid}`;
        } else if (movie.fanart_uuid) {
            imageUrl = `/api/images/${movie.fanart_uuid}`;
        } else if (movie.thumb_uuid) {
            imageUrl = `/api/images/${movie.thumb_uuid}`;
        }

        // 格式化评分
        const rating = movie.rating ? parseFloat(movie.rating).toFixed(1) : '';

        // 格式化时长
        const runtime = movie.runtime ? `${movie.runtime}分钟` : '';

        // 获取系列名称
        const seriesName = movie.series_name || '';

        // 格式化年份
        const year = movie.year || '';

        return `
            <tr class="hover:bg-base-200 cursor-pointer transition-colors movie-list-item" data-movie-id="${movie.id}">
                <!-- 封面缩略图 -->
                <td class="p-2">
                    <div class="avatar">
                        <div class="w-10 sm:w-12 h-14 sm:h-16 rounded">
                            ${imageUrl ?
                                `<img src="${imageUrl}"
                                     alt="${movie.title}"
                                     class="w-full h-full object-cover"
                                     loading="lazy"
                                     onerror="this.onerror=null; this.classList.add('hidden'); this.nextElementSibling.classList.remove('hidden');">
                                 <div class="w-full h-full flex items-center justify-center bg-base-200 text-base-content/50 hidden">
                                    <i class="bi bi-film text-sm sm:text-lg" aria-label="默认海报图标"></i>
                                </div>` :
                                `<div class="w-full h-full flex items-center justify-center bg-base-200 text-base-content/50">
                                    <i class="bi bi-film text-sm sm:text-lg" aria-label="默认海报图标"></i>
                                </div>`
                            }
                        </div>
                    </div>
                </td>

                <!-- 标题 -->
                <td class="p-2">
                    <div class="flex items-center gap-2">
                        <div class="font-medium text-base-content line-clamp-2 flex-1" title="${this.escapeHtml(movie.title)}">
                            ${this.escapeHtml(movie.title)}
                        </div>
                        ${movie.is_updated ? `
                            <div class="badge badge-info badge-sm">
                                <i class="bi bi-arrow-clockwise mr-1" aria-label="已更新图标"></i>
                                已更新
                            </div>
                        ` : ''}
                    </div>
                    <!-- 移动端显示的额外信息 -->
                    <div class="text-xs text-base-content/70 mt-1 sm:hidden">
                        <div class="flex flex-wrap gap-2">
                            ${rating ? `
                                <span class="badge badge-primary badge-xs">
                                    <i class="bi bi-star-fill mr-1" aria-label="评分图标"></i>
                                    ${rating}
                                </span>
                            ` : ''}
                            ${year ? `<span class="badge badge-outline badge-xs">${year}</span>` : ''}
                            ${runtime ? `<span class="badge badge-outline badge-xs">${runtime}</span>` : ''}
                        </div>
                        ${seriesName ? `
                            <div class="mt-1 text-xs text-base-content/60 truncate" title="${this.escapeHtml(seriesName)}">
                                系列: ${this.escapeHtml(seriesName)}
                            </div>
                        ` : ''}
                    </div>
                </td>

                <!-- 评分 -->
                <td class="p-2 hidden sm:table-cell">
                    ${rating ? `
                        <div class="badge badge-primary badge-sm">
                            <i class="bi bi-star-fill mr-1" aria-label="评分图标"></i>
                            ${rating}
                        </div>
                    ` : '<span class="text-base-content/50">-</span>'}
                </td>

                <!-- 系列 -->
                <td class="p-2 hidden md:table-cell">
                    ${seriesName ? `
                        <span class="text-sm text-base-content/80 truncate" title="${this.escapeHtml(seriesName)}">
                            ${this.escapeHtml(seriesName)}
                        </span>
                    ` : '<span class="text-base-content/50">-</span>'}
                </td>

                <!-- 年份 -->
                <td class="p-2 hidden lg:table-cell">
                    ${year ? `
                        <span class="text-sm text-base-content/80">
                            ${year}
                        </span>
                    ` : '<span class="text-base-content/50">-</span>'}
                </td>

                <!-- 时长 -->
                <td class="p-2 hidden lg:table-cell">
                    ${runtime ? `
                        <span class="text-sm text-base-content/80">
                            ${runtime}
                        </span>
                    ` : '<span class="text-base-content/50">-</span>'}
                </td>

                <!-- 操作按钮 -->
                <td class="p-2">
                    <div class="flex items-center gap-1">
                        <!-- 收藏按钮容器 -->
                        <div class="favorite-btn-container" data-movie-id="${movie.id}">
                            <!-- 收藏按钮将在这里动态生成 -->
                        </div>

                        <!-- 批量选择复选框 -->
                        <div class="batch-select-container hidden">
                            <input type="checkbox" class="checkbox checkbox-primary checkbox-sm batch-select-checkbox" data-movie-id="${movie.id}">
                        </div>
                    </div>
                </td>
            </tr>
        `;
    }

    /**
     * 创建影片卡片
     */
    createMovieCard(movie) {
        // 构建图片URL
        let imageUrl = '';
        if (movie.poster_uuid) {
            imageUrl = `/api/images/${movie.poster_uuid}`;
        } else if (movie.fanart_uuid) {
            imageUrl = `/api/images/${movie.fanart_uuid}`;
        } else if (movie.thumb_uuid) {
            imageUrl = `/api/images/${movie.thumb_uuid}`;
        }

        // 格式化评分
        const rating = movie.rating ? parseFloat(movie.rating).toFixed(1) : '';

        // 格式化时长
        const runtime = movie.runtime ? `${movie.runtime}分钟` : '';

        return `
            <div class="card bg-base-100 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer group movie-card" data-movie-id="${movie.id}">
                <figure class="relative aspect-[2/3] overflow-hidden">
                    ${imageUrl ?
                        `<img src="${imageUrl}"
                             alt="${movie.title}"
                             class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                             loading="lazy"
                             onerror="this.onerror=null; this.classList.add('hidden'); this.nextElementSibling.classList.remove('hidden'); this.nextElementSibling.classList.add('flex');">
                         <div class="absolute inset-0 flex items-center justify-center bg-base-200 text-base-content/50 hidden">
                            <i class="bi bi-film" style="font-size: 3rem;" aria-label="默认海报图标"></i>
                        </div>` :
                        `<div class="absolute inset-0 flex items-center justify-center bg-base-200 text-base-content/50">
                            <i class="bi bi-film" style="font-size: 3rem;" aria-label="默认海报图标"></i>
                        </div>`
                    }

                    <!-- 批量选择复选框 -->
                    <div class="batch-select-container absolute top-2 left-2 z-20 hidden p-1">
                        <input type="checkbox" class="checkbox checkbox-primary batch-select-checkbox" data-movie-id="${movie.id}">
                    </div>

                    <!-- 收藏状态指示器（始终可见，在批量模式下调整位置） -->
                    <div class="absolute top-2 right-2 z-10 favorite-status-indicator transition-all duration-300"
                         data-movie-id="${movie.id}"
                         style="transform: translateX(0);">
                        <!-- 收藏状态图标将在这里动态生成 -->
                    </div>

                    <!-- 收藏按钮容器（悬停时显示） -->
                    <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20 favorite-btn-container" data-movie-id="${movie.id}">
                        <!-- 收藏按钮将在这里动态生成 -->
                    </div>

                    <!-- 悬停时显示的播放图标 -->
                    <div class="absolute inset-0 flex items-center justify-center bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div class="btn btn-circle btn-primary btn-lg">
                            <i class="bi bi-play-fill" style="font-size: 1.5rem;" aria-label="播放图标"></i>
                        </div>
                    </div>

                    <!-- 评分标签 -->
                    ${rating ? `
                        <div class="absolute bottom-2 left-2">
                            <div class="badge badge-primary badge-sm">
                                <i class="bi bi-star-fill mr-1" aria-label="评分图标"></i>
                                ${rating}
                            </div>
                        </div>
                    ` : ''}

                </figure>

                <div class="card-body p-2">
                    <h3 class="text-xs font-medium line-clamp-2" title="${this.escapeHtml(movie.title)}">
                        ${this.escapeHtml(movie.title)}
                    </h3>

                    <div class="flex flex-col gap-1 text-xs text-base-content/70">
                        ${movie.year ? `
                            <div class="flex items-center gap-1">
                                <i class="bi bi-collection" aria-label="年份"></i>
                                <span class="truncate">${movie.year}</span>
                            </div>
                        ` : ''}

                        ${runtime ? `
                            <div class="flex items-center gap-1">
                                <i class="bi bi-clock" aria-label="时长图标"></i>
                                <span>${runtime}</span>
                            </div>
                        ` : ''}
                    </div>

                    <!-- 统计信息 -->
                    <div class="flex justify-between items-center mt-2 text-xs text-base-content/60">
                        <div class="flex items-center gap-2">
                            ${movie.genre_count > 0 ? `
                                <span class="flex items-center gap-1">
                                    <i class="bi bi-tags" aria-label="分类图标"></i>
                                    ${movie.genre_count}
                                </span>
                            ` : ''}
                            ${movie.tag_count > 0 ? `
                                <span class="flex items-center gap-1">
                                    <i class="bi bi-bookmark" aria-label="标签图标"></i>
                                    ${movie.tag_count}
                                </span>
                            ` : ''}
                            ${movie.actor_count > 0 ? `
                                <span class="flex items-center gap-1">
                                    <i class="bi bi-people" aria-label="演员图标"></i>
                                    ${movie.actor_count}
                                </span>
                            ` : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 显示影片详情
     */
    async showMovieDetail(movieId) {
        // 跳转到影片详情页面
        // 由于当前使用的是数据库ID，所以使用info路由
        window.location.href = `/movies/info/${movieId}`;
    }

    /**
     * 更新分页
     */
    updatePagination() {
        const totalPages = Math.ceil(this.totalCount / this.pageSize);
        const paginationInfo = document.getElementById('pagination-info');
        const paginationControls = document.getElementById('pagination-controls');

        if (paginationInfo) {
            const start = (this.currentPage - 1) * this.pageSize + 1;
            const end = Math.min(this.currentPage * this.pageSize, this.totalCount);
            paginationInfo.textContent = `第 ${this.currentPage} 页，共 ${totalPages} 页 (显示 ${start} - ${end} 条，共 ${this.totalCount} 条记录)`;
        }

        if (paginationControls && totalPages > 1) {
            paginationControls.innerHTML = this.generatePaginationHtml(totalPages);
            this.bindPaginationEvents();
        } else if (paginationControls) {
            paginationControls.innerHTML = '';
        }
    }

    /**
     * 生成分页 HTML
     */
    generatePaginationHtml(totalPages) {
        let html = '';

        // 上一页
        html += `
            <button class="join-item btn ${this.currentPage === 1 ? 'btn-disabled' : ''}" data-page="${this.currentPage - 1}" ${this.currentPage === 1 ? 'disabled' : ''}>
                <i class="bi bi-chevron-left" aria-label="上一页图标"></i>
                上一页
            </button>
        `;

        // 下一页
        html += `
            <button class="join-item btn ${this.currentPage === totalPages ? 'btn-disabled' : ''}" data-page="${this.currentPage + 1}" ${this.currentPage === totalPages ? 'disabled' : ''}>
                下一页
                <i class="bi bi-chevron-right" aria-label="下一页图标"></i>
            </button>
        `;

        return html;
    }

    /**
     * 绑定分页事件
     */
    bindPaginationEvents() {
        const paginationButtons = document.querySelectorAll('#pagination-controls .btn[data-page]');
        paginationButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const page = parseInt(button.getAttribute('data-page'));
                if (page && page !== this.currentPage && !button.disabled) {
                    this.currentPage = page;
                    this.loadMovies();
                    this.updateUrl();
                }
            });
        });
    }

    /**
     * 更新过滤信息
     */
    updateFilterInfo() {
        const filterInfo = document.getElementById('filter-info');
        const filterCount = document.getElementById('filter-count');

        if (!filterInfo) return;

        const activeFilters = [];

        if (this.filters.search) {
            activeFilters.push(`"${this.filters.search}"`);
        }

        if (this.filters.tag_ids && this.filters.tag_ids.length > 0) {
            activeFilters.push(`标签(${this.filters.tag_ids.length})`);
        }

        if (this.filters.genre_ids && this.filters.genre_ids.length > 0) {
            activeFilters.push(`分类(${this.filters.genre_ids.length})`);
        }

        if (this.filters.series_ids && this.filters.series_ids.length > 0) {
            activeFilters.push(`系列(${this.filters.series_ids.length})`);
        }

        if (this.filters.year_from || this.filters.year_to) {
            const yearRange = `${this.filters.year_from || ''}${this.filters.year_from && this.filters.year_to ? '-' : ''}${this.filters.year_to || ''}`;
            activeFilters.push(`年份:${yearRange}`);
        }

        if (this.filters.rating_from || this.filters.rating_to) {
            const ratingRange = `${this.filters.rating_from || ''}${this.filters.rating_from && this.filters.rating_to ? '-' : ''}${this.filters.rating_to || ''}⭐`;
            activeFilters.push(`评分:${ratingRange}`);
        }

        // 更新过滤信息徽章
        if (activeFilters.length > 0) {
            filterInfo.innerHTML = `
                <i class="bi bi-funnel mr-1" aria-label="筛选图标"></i>
                ${activeFilters.join(' • ')}
            `;
            filterInfo.className = 'badge badge-primary badge-lg';
        } else {
            filterInfo.innerHTML = `
                <i class="bi bi-funnel mr-1" aria-label="筛选图标"></i>
                显示所有影片
            `;
            filterInfo.className = 'badge badge-outline badge-lg';
        }

        // 更新影片数量显示
        if (filterCount) {
            filterCount.textContent = `共 ${this.totalCount} 部影片`;
        }
    }

    /**
     * 执行搜索
     */
    performSearch() {
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            this.filters.search = searchInput.value.trim();
            this.currentPage = 1;
            this.loadMovies();
            this.updateUrl();
        }
    }

    /**
     * 应用高级搜索筛选
     */
    applyAdvancedFilters(advancedFilters) {
        // 更新筛选条件
        this.filters.tag_ids = advancedFilters.tags || [];
        this.filters.genre_ids = advancedFilters.genres || [];
        this.filters.series_ids = advancedFilters.series || [];
        this.filters.actor_ids = advancedFilters.actors || [];
        this.filters.year_from = advancedFilters.yearFrom || '';
        this.filters.year_to = advancedFilters.yearTo || '';
        this.filters.rating_from = advancedFilters.ratingFrom || '';
        this.filters.rating_to = advancedFilters.ratingTo || '';

        // 处理更新状态筛选
        if (advancedFilters.isUpdated === 'true') {
            this.filters.is_updated = true;
        } else if (advancedFilters.isUpdated === 'false') {
            this.filters.is_updated = false;
        } else {
            this.filters.is_updated = null;
        }

        // 显示/隐藏批量NFO同步按钮
        this.toggleBatchNfoSyncButton(this.filters.is_updated === true);

        // 重新加载影片列表
        this.currentPage = 1;
        this.loadMovies();
        this.updateUrl();
    }

    /**
     * 设置排序方式
     * @param {string} sortBy - 排序字段
     */
    setSortOrder(sortBy) {
        // 如果点击的是当前排序字段，则切换排序方向
        if (this.sortBy === sortBy) {
            this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortBy = sortBy;
            this.sortOrder = 'desc'; // 默认降序
        }

        // 重新加载影片列表
        this.currentPage = 1;
        this.loadMovies();
        this.updateUrl();

        // 更新排序按钮显示
        this.updateSortDisplay();
    }

    /**
     * 更新排序显示
     */
    updateSortDisplay() {
        const sortButtons = document.querySelectorAll('[data-sort]');
        sortButtons.forEach(button => {
            const sortBy = button.getAttribute('data-sort');
            const isActive = sortBy === this.sortBy;

            // 处理表格头部的排序显示
            if (button.tagName === 'TH') {
                const arrowIcon = button.querySelector('.bi-arrow-down-up');
                if (arrowIcon) {
                    if (isActive) {
                        button.classList.add('bg-base-300');
                        arrowIcon.className = this.sortOrder === 'asc' ? 'bi bi-arrow-up text-xs' : 'bi bi-arrow-down text-xs';
                    } else {
                        button.classList.remove('bg-base-300');
                        arrowIcon.className = 'bi bi-arrow-down-up text-xs opacity-50';
                    }
                }
            } else {
                // 处理其他排序按钮
                if (isActive) {
                    button.classList.add('btn-active');
                    // 添加排序方向指示
                    const arrow = this.sortOrder === 'asc' ? '↑' : '↓';
                    const textContent = button.textContent.replace(/[↑↓]$/, '').trim();
                    button.innerHTML = button.innerHTML.replace(textContent, `${textContent} ${arrow}`);
                } else {
                    button.classList.remove('btn-active');
                    button.innerHTML = button.innerHTML.replace(/\s[↑↓]$/, '');
                }
            }
        });
    }

    /**
     * 清除所有筛选
     */
    clearFilters() {
        // 重置过滤器
        this.filters = {
            search: '',
            tag_ids: [],
            genre_ids: [],
            series_ids: [],
            year_from: '',
            year_to: '',
            rating_from: '',
            rating_to: '',
            directory_id: null,
            is_updated: null
        };

        // 重置表单
        const searchInput = document.getElementById('search-input');
        if (searchInput) searchInput.value = '';

        // 重置高级搜索
        if (window.advancedSearch) {
            window.advancedSearch.resetFilters();
        }

        // 隐藏目录筛选信息
        this.hideDirectoryFilterInfo();

        // 重置筛选按钮状态
        this.updateFilterButtonStates('all');

        // 隐藏批量NFO同步按钮
        this.toggleBatchNfoSyncButton(false);

        // 重置页码
        this.currentPage = 1;

        // 重新加载
        this.loadMovies();
        this.updateUrl();
    }

    /**
     * 从 URL 和 sessionStorage 初始化状态
     */
    initializeFromUrl() {
        const params = utils.getUrlParams();

        if (params.search) {
            this.filters.search = params.search;
            const searchInput = document.getElementById('search-input');
            if (searchInput) searchInput.value = params.search;
        }

        // 处理目录筛选参数 - 优先从URL参数读取，然后从sessionStorage读取
        if (params.directory_id) {
            this.filters.directory_id = parseInt(params.directory_id);
            this.showDirectoryFilterInfo(this.filters.directory_id);
        } else {
            const selectedDirectoryId = sessionStorage.getItem('selectedDirectoryId');
            if (selectedDirectoryId) {
                this.filters.directory_id = parseInt(selectedDirectoryId);
                this.showDirectoryFilterInfo(this.filters.directory_id);
                // 清除sessionStorage中的值，避免影响后续访问
                sessionStorage.removeItem('selectedDirectoryId');
            }
        }

        // 处理筛选参数
        if (params.tags) {
            this.filters.tag_ids = params.tags.split(',').map(id => parseInt(id)).filter(id => !isNaN(id));
        }
        if (params.genres) {
            this.filters.genre_ids = params.genres.split(',').map(id => parseInt(id)).filter(id => !isNaN(id));
        }
        if (params.series) {
            this.filters.series_ids = params.series.split(',').map(id => parseInt(id)).filter(id => !isNaN(id));
        }
        if (params.actors) {
            this.filters.actor_ids = params.actors.split(',').map(id => parseInt(id)).filter(id => !isNaN(id));
        }
        if (params.year_from) {
            this.filters.year_from = params.year_from;
        }
        if (params.year_to) {
            this.filters.year_to = params.year_to;
        }
        if (params.rating_from) {
            this.filters.rating_from = params.rating_from;
        }
        if (params.rating_to) {
            this.filters.rating_to = params.rating_to;
        }

        // 处理更新状态筛选参数
        if (params.is_updated !== undefined) {
            if (params.is_updated === 'true') {
                this.filters.is_updated = true;
                this.updateFilterButtonStates('updated');
                this.toggleBatchNfoSyncButton(true);
            } else if (params.is_updated === 'false') {
                this.filters.is_updated = false;
                this.updateFilterButtonStates('not-updated');
                this.toggleBatchNfoSyncButton(false);
            } else {
                this.filters.is_updated = null;
                this.updateFilterButtonStates('all');
                this.toggleBatchNfoSyncButton(false);
            }
        }

        // 高级搜索参数处理将在高级搜索模块中处理

        if (params.page) {
            this.currentPage = parseInt(params.page) || 1;
        }
    }

    /**
     * 更新 URL
     */
    updateUrl() {
        const params = {};

        if (this.filters.search) params.search = this.filters.search;
        if (this.filters.tag_ids && this.filters.tag_ids.length > 0) params.tags = this.filters.tag_ids.join(',');
        if (this.filters.genre_ids && this.filters.genre_ids.length > 0) params.genres = this.filters.genre_ids.join(',');
        if (this.filters.series_ids && this.filters.series_ids.length > 0) params.series = this.filters.series_ids.join(',');
        if (this.filters.actor_ids && this.filters.actor_ids.length > 0) params.actors = this.filters.actor_ids.join(',');
        if (this.filters.year_from) params.year_from = this.filters.year_from;
        if (this.filters.year_to) params.year_to = this.filters.year_to;
        if (this.filters.rating_from) params.rating_from = this.filters.rating_from;
        if (this.filters.rating_to) params.rating_to = this.filters.rating_to;
        if (this.filters.directory_id) params.directory_id = this.filters.directory_id;
        if (this.filters.is_updated !== null) params.is_updated = this.filters.is_updated;
        if (this.currentPage > 1) params.page = this.currentPage;

        utils.setUrlParams(params, true);
    }

    /**
     * 显示加载状态
     */
    showLoading() {
        if (this.viewMode === 'grid') {
            const skeleton = document.getElementById('loading-skeleton');
            if (skeleton) {
                skeleton.innerHTML = this.generateSkeletonHtml();
                skeleton.classList.remove('hidden');
            }
        } else {
            const listSkeleton = document.getElementById('loading-skeleton-list');
            if (listSkeleton) {
                listSkeleton.classList.remove('hidden');
            }
        }
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        const skeleton = document.getElementById('loading-skeleton');
        const listSkeleton = document.getElementById('loading-skeleton-list');

        if (skeleton) {
            skeleton.classList.add('hidden');
        }
        if (listSkeleton) {
            listSkeleton.classList.add('hidden');
        }
    }

    /**
     * 生成骨架屏 HTML
     */
    generateSkeletonHtml() {
        let html = '';
        for (let i = 0; i < this.pageSize; i++) {
            html += `
                <div class="card bg-base-100 shadow-lg">
                    <figure class="relative aspect-[2/3] overflow-hidden">
                        <div class="skeleton w-full h-full"></div>
                    </figure>
                    <div class="card-body p-4">
                        <div class="skeleton h-4 w-full mb-2"></div>
                        <div class="skeleton h-4 w-full mb-2"></div>
                        <div class="skeleton h-3 w-3/4 mb-3"></div>
                        <div class="skeleton h-4 w-1/2 mb-3"></div>
                        <div class="flex gap-2">
                            <div class="skeleton h-6 w-12 rounded-full"></div>
                            <div class="skeleton h-6 w-12 rounded-full"></div>
                        </div>
                    </div>
                </div>
            `;
        }
        return html;
    }

    /**
     * 显示空状态
     */
    showEmptyState() {
        const emptyState = document.getElementById('empty-state');
        if (emptyState) {
            emptyState.classList.remove('hidden');
        }
    }

    /**
     * 显示错误状态
     */
    showErrorState(message) {
        const errorState = document.getElementById('error-state');
        const errorMessage = document.getElementById('error-message');

        if (errorState) {
            errorState.classList.remove('hidden');
        }

        if (errorMessage) {
            errorMessage.textContent = message;
        }
    }

    /**
     * 隐藏所有状态
     */
    hideStates() {
        const states = ['empty-state', 'error-state'];
        states.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.classList.add('hidden');
            }
        });

        // 清空影片容器内容，避免显示旧数据
        const gridContainer = document.getElementById('movies-container');
        const listBody = document.getElementById('movies-list-body');

        if (gridContainer) {
            gridContainer.innerHTML = '';
        }
        if (listBody) {
            listBody.innerHTML = '';
        }
    }

    /**
     * 显示目录筛选信息
     * @param {number} directoryId - 目录ID
     */
    async showDirectoryFilterInfo(directoryId) {
        const filterInfo = document.getElementById('directory-filter-info');
        const directoryNameEl = document.getElementById('current-directory-name');

        if (!filterInfo || !directoryNameEl) return;

        try {
            // 获取目录信息
            const response = await api.getDirectories();
            if (response.success && response.data) {
                const directory = response.data.find(d => d.id === directoryId);
                if (directory) {
                    directoryNameEl.textContent = directory.name;
                    filterInfo.classList.remove('hidden');
                } else {
                    // 如果找不到目录，显示未知目录
                    directoryNameEl.textContent = '未知目录';
                    filterInfo.classList.remove('hidden');
                }
            }
        } catch (error) {
            console.error('获取目录信息失败:', error);
            // 显示备选文本
            directoryNameEl.textContent = '目录筛选';
            filterInfo.classList.remove('hidden');
        }
    }

    /**
     * 隐藏目录筛选信息
     */
    hideDirectoryFilterInfo() {
        const filterInfo = document.getElementById('directory-filter-info');
        if (filterInfo) {
            filterInfo.classList.add('hidden');
        }
    }

    /**
     * 清除目录筛选
     */
    clearDirectoryFilter() {
        this.filters.directory_id = null;
        this.hideDirectoryFilterInfo();
        this.currentPage = 1;
        this.loadMovies();
        this.updateUrl();
    }

    /**
     * 设置目录筛选
     * @param {number} directoryId - 目录ID
     */
    setDirectoryFilter(directoryId) {
        this.filters.directory_id = directoryId;
        this.showDirectoryFilterInfo(directoryId);
        this.currentPage = 1;
        this.loadMovies();
        this.updateUrl();
    }

    /**
     * 初始化批量工具栏
     */
    initBatchToolbar() {
        if (window.BatchToolbar) {
            this.batchToolbar = new window.BatchToolbar('movies-container');
        }
    }

    /**
     * 切换批量选择模式
     */
    toggleBatchMode() {
        if (!window.favoriteManager) return;

        const isCurrentlyBatchMode = window.favoriteManager.isBatchMode();
        const newBatchMode = !isCurrentlyBatchMode;

        // 切换批量模式
        window.favoriteManager.toggleBatchMode(newBatchMode);

        // 更新批量选择按钮状态
        this.updateBatchSelectButton(newBatchMode);
    }

    /**
     * 更新批量选择按钮状态
     * @param {boolean} batchMode - 是否批量模式
     */
    updateBatchSelectButton(batchMode) {
        const batchSelectBtn = document.getElementById('batch-select-btn');
        if (!batchSelectBtn) return;

        const icon = batchSelectBtn.querySelector('i');

        if (batchMode) {
            batchSelectBtn.classList.remove('btn-outline');
            batchSelectBtn.classList.add('btn-primary');
            icon.className = 'bi bi-check-square-fill';
            batchSelectBtn.innerHTML = `
                <i class="bi bi-check-square-fill" aria-label="批量选择图标"></i>
                退出批量
            `;
        } else {
            batchSelectBtn.classList.remove('btn-primary');
            batchSelectBtn.classList.add('btn-outline');
            icon.className = 'bi bi-check-square';
            batchSelectBtn.innerHTML = `
                <i class="bi bi-check-square" aria-label="批量选择图标"></i>
                批量选择
            `;
        }
    }

    /**
     * 更新影片显示后初始化收藏状态
     */
    async updateMoviesDisplayWithFavorites() {
        if (!window.favoriteManager) return;

        const gridContainer = document.getElementById('movies-container');
        const listContainer = document.getElementById('movies-list-body');
        const activeContainer = this.viewMode === 'grid' ? gridContainer : listContainer;

        if (!activeContainer) return;

        // 直接使用API返回的收藏状态，无需额外API调用
        this.movies.forEach(movie => {
            const movieId = movie.id;
            const isFavorited = movie.is_favorited || false;

            // 创建收藏按钮
            const btnContainer = activeContainer.querySelector(`.favorite-btn-container[data-movie-id="${movieId}"]`);
            if (btnContainer && window.favoriteManager) {
                const buttonHtml = window.favoriteManager.createFavoriteButton(
                    movieId,
                    isFavorited,
                    'sm'
                );
                btnContainer.innerHTML = buttonHtml;
            }

            // 对于网格视图，还需要创建收藏状态指示器
            if (this.viewMode === 'grid') {
                const statusIndicator = activeContainer.querySelector(`.favorite-status-indicator[data-movie-id="${movieId}"]`);
                if (statusIndicator) {
                    this.updateFavoriteStatusIndicator(statusIndicator, isFavorited);
                }
            }
        });
    }

    /**
     * 更新收藏状态指示器
     * @param {HTMLElement} indicator - 状态指示器元素
     * @param {boolean} isFavorited - 是否已收藏
     */
    updateFavoriteStatusIndicator(indicator, isFavorited) {
        const movieId = indicator.dataset.movieId;
        if (!movieId || !window.favoriteManager) return;

        if (isFavorited) {
            // 使用 favoriteManager 的方法创建与收藏按钮一致的外观
            const indicatorHtml = window.favoriteManager.createFavoriteStatusIndicator(
                parseInt(movieId),
                true,
                'sm'
            );
            indicator.innerHTML = indicatorHtml;
            indicator.classList.remove('hidden');
        } else {
            indicator.innerHTML = '';
            indicator.classList.add('hidden');
        }
    }

    /**
     * 初始化收藏状态监听器
     */
    initFavoriteStatusListener() {
        document.addEventListener('favoriteStatusChanged', (event) => {
            const { movieId, isFavorited } = event.detail;

            // 更新本地影片数据中的收藏状态
            const movie = this.movies.find(m => m.id === movieId);
            if (movie) {
                movie.is_favorited = isFavorited;
            }
        });
    }

    /**
     * HTML 转义
     */
    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 初始化视图模式
     */
    initViewMode() {
        // 从本地存储读取视图模式
        const savedViewMode = localStorage.getItem(this.VIEW_MODE_STORAGE_KEY);
        if (savedViewMode && (savedViewMode === 'grid' || savedViewMode === 'list')) {
            this.viewMode = savedViewMode;
        }

        // 更新视图切换按钮状态
        this.updateViewToggleButton();

        // 设置初始容器样式
        this.updateContainerLayout();
    }

    /**
     * 切换视图模式
     */
    toggleViewMode() {
        this.viewMode = this.viewMode === 'grid' ? 'list' : 'grid';

        // 保存到本地存储
        localStorage.setItem(this.VIEW_MODE_STORAGE_KEY, this.viewMode);

        // 更新按钮状态
        this.updateViewToggleButton();

        // 更新容器布局
        this.updateContainerLayout();

        // 重新渲染影片列表
        this.updateMoviesDisplay();

        // 更新排序显示
        this.updateSortDisplay();
    }

    /**
     * 更新视图切换按钮状态
     */
    updateViewToggleButton() {
        const toggleBtn = document.getElementById('view-toggle-btn');
        if (!toggleBtn) return;

        const icon = toggleBtn.querySelector('i');
        if (this.viewMode === 'grid') {
            icon.className = 'bi bi-grid';
            toggleBtn.title = '切换到列表视图';
        } else {
            icon.className = 'bi bi-list';
            toggleBtn.title = '切换到网格视图';
        }
    }

    /**
     * 更新容器布局
     */
    updateContainerLayout() {
        const gridContainer = document.getElementById('movies-container');
        const listContainer = document.getElementById('movies-list-container');
        const gridSkeleton = document.getElementById('loading-skeleton');
        const listSkeleton = document.getElementById('loading-skeleton-list');

        if (!gridContainer || !listContainer) return;

        if (this.viewMode === 'grid') {
            // 显示网格视图，隐藏列表视图
            gridContainer.classList.remove('hidden');
            listContainer.classList.add('hidden');

            // 重置网格容器样式
            gridContainer.className = 'grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-4';

            if (gridSkeleton) {
                gridSkeleton.className = 'grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-4 hidden';
            }
        } else {
            // 显示列表视图，隐藏网格视图
            gridContainer.classList.add('hidden');
            listContainer.classList.remove('hidden');

            if (listSkeleton) {
                listSkeleton.className = 'overflow-x-auto hidden';
            }
        }
    }

    /**
     * 设置更新状态筛选
     * @param {string} filterType - 筛选类型: 'all', 'updated', 'not-updated'
     */
    setUpdatedFilter(filterType) {
        // 更新筛选参数
        switch (filterType) {
            case 'all':
                this.filters.is_updated = null;
                break;
            case 'updated':
                this.filters.is_updated = true;
                break;
            case 'not-updated':
                this.filters.is_updated = false;
                break;
        }

        // 更新按钮状态
        this.updateFilterButtonStates(filterType);

        // 显示/隐藏批量NFO同步按钮
        this.toggleBatchNfoSyncButton(filterType === 'updated');

        // 重置页码并重新加载
        this.currentPage = 1;
        this.loadMovies();
        this.updateUrl();
    }

    /**
     * 更新筛选按钮状态
     * @param {string} activeFilter - 当前激活的筛选类型
     */
    updateFilterButtonStates(activeFilter) {
        const filterButtons = document.querySelectorAll('[data-filter]');
        filterButtons.forEach(button => {
            const filterType = button.getAttribute('data-filter');
            if (filterType === activeFilter) {
                button.classList.add('btn-primary');
                button.classList.remove('btn-outline');
            } else {
                button.classList.remove('btn-primary');
                button.classList.add('btn-outline');
            }
        });

        // 更新筛选状态显示
        this.updateFilterBadge(activeFilter);
    }

    /**
     * 更新筛选状态徽章显示
     * @param {string} filterType - 筛选类型
     */
    updateFilterBadge(filterType) {
        const badge = document.getElementById('current-filter-badge');
        const text = document.getElementById('current-filter-text');
        const icon = badge?.querySelector('i');

        if (!badge || !text || !icon) return;

        // 重置样式，保持高度和基础样式
        badge.className = 'badge badge-outline h-8 md:h-10 px-3 flex items-center';

        switch (filterType) {
            case 'all':
                icon.className = 'bi bi-list mr-1 text-xs';
                text.textContent = '全部';
                badge.classList.add('badge-outline');
                break;
            case 'updated':
                icon.className = 'bi bi-arrow-clockwise mr-1 text-xs';
                text.textContent = '已更新';
                badge.classList.add('badge-success');
                break;
            case 'not-updated':
                icon.className = 'bi bi-clock mr-1 text-xs';
                text.textContent = '未更新';
                badge.classList.add('badge-warning');
                break;
            default:
                icon.className = 'bi bi-list mr-1 text-xs';
                text.textContent = '全部';
                badge.classList.add('badge-outline');
        }
    }
    /**
     * 显示/隐藏批量NFO同步按钮
     * @param {boolean} show - 是否显示按钮
     */
    toggleBatchNfoSyncButton(show) {
        // 搜索区域的批量NFO同步按钮
        const searchAreaBtn = document.getElementById('batch-nfo-sync-btn');
        if (searchAreaBtn) {
            if (show) {
                searchAreaBtn.classList.remove('hidden');
            } else {
                searchAreaBtn.classList.add('hidden');
            }
        }

        // 兼容性：原始容器（如果存在）
        const container = document.getElementById('batch-nfo-sync-container');
        if (container) {
            if (show) {
                container.classList.remove('hidden');
            } else {
                container.classList.add('hidden');
            }
        }
    }

    /**
     * 处理批量NFO同步按钮点击
     */
    async handleBatchNfoSync() {
        try {

            // 显示确认对话框前，先获取已更新影片数量
            const requestParams = {
                is_updated: true,
                limit: 1,  // 只需要获取总数
                offset: 0
            };

            const response = await api.getMovies(requestParams);

            const totalCount = response.total_count || 0;

            if (totalCount === 0) {
                console.warn('没有找到已更新的影片');
                if (window.toast) {
                    window.toast.info('没有找到需要同步的已更新影片');
                }
                return;
            }

            // 更新确认对话框中的影片数量
            const countElement = document.getElementById('batch-nfo-count');
            if (countElement) {
                countElement.textContent = totalCount;
            }

            // 显示确认对话框
            const confirmModal = document.getElementById('batch-nfo-sync-confirm-modal');
            if (confirmModal) {
                confirmModal.showModal();
            }

        } catch (error) {
            console.error('获取已更新影片数量失败:', error);
            if (window.toast) {
                window.toast.error('获取影片信息失败，请稍后重试');
            }
        }
    }

    /**
     * 执行批量NFO同步
     */
    async executeBatchNfoSync() {
        try {
            // 关闭确认对话框
            const confirmModal = document.getElementById('batch-nfo-sync-confirm-modal');
            if (confirmModal) {
                confirmModal.close();
            }

            // 显示进度对话框
            const progressModal = document.getElementById('batch-nfo-sync-progress-modal');
            if (progressModal) {
                progressModal.showModal();
            }

            // 重置进度显示
            this.updateBatchNfoProgress(0, 0, '准备开始同步...');

            // 启动批量同步任务
            const response = await api.batchSyncUpdatedNfo();

            if (response.success && response.data?.task_id) {
                // 开始监控进度（总数将从进度数据中获取）
                this.monitorNfoSyncProgress(response.data.task_id, 0);
            } else {
                throw new Error(response.message || '启动同步任务失败');
            }

        } catch (error) {
            console.error('批量NFO同步失败:', error);

            // 关闭进度对话框
            const progressModal = document.getElementById('batch-nfo-sync-progress-modal');
            if (progressModal) {
                progressModal.close();
            }

            // 显示错误结果
            this.showBatchNfoSyncResult({
                success: false,
                message: '批量同步失败：' + (error.message || '网络错误，请稍后重试'),
                data: {
                    total_count: 0,
                    success_count: 0,
                    failed_count: 0,
                    errors: [error.message || '网络错误']
                }
            });
        }
    }

    /**
     * 监控NFO同步进度
     * @param {string} taskId - 任务ID
     * @param {number} totalCount - 总数量
     */
    async monitorNfoSyncProgress(taskId, totalCount) {
        this.nfoSyncTaskId = taskId;

        const checkProgress = async () => {
            try {
                const response = await api.getNfoSyncProgress(taskId);

                if (response.success && response.data) {
                    const progress = response.data;

                    // 更新进度显示
                    this.updateBatchNfoProgress(
                        progress.current_index || 0,
                        progress.total_count || totalCount,
                        progress.current_movie_title || '处理中...'
                    );

                    // 检查任务状态
                    if (progress.status === 'completed' || progress.status === 'error' || progress.status === 'cancelled') {
                        // 任务完成，停止监控
                        clearInterval(this.nfoSyncProgressInterval);
                        this.nfoSyncProgressInterval = null;

                        // 关闭进度对话框
                        const progressModal = document.getElementById('batch-nfo-sync-progress-modal');
                        if (progressModal) {
                            progressModal.close();
                        }

                        // 显示结果
                        this.showBatchNfoSyncResultFromProgress(progress);

                        // 如果有成功同步的影片，刷新影片列表
                        if (progress.success_count > 0) {
                            this.loadMovies();
                        }
                    }
                }
            } catch (error) {
                console.error('获取NFO同步进度失败:', error);
                // 继续监控，可能是临时网络问题
            }
        };

        // 立即检查一次
        await checkProgress();

        // 每2秒检查一次进度
        this.nfoSyncProgressInterval = setInterval(checkProgress, 2000);

        // 设置最大监控时间（30分钟）
        setTimeout(() => {
            if (this.nfoSyncProgressInterval) {
                clearInterval(this.nfoSyncProgressInterval);
                this.nfoSyncProgressInterval = null;

                // 关闭进度对话框
                const progressModal = document.getElementById('batch-nfo-sync-progress-modal');
                if (progressModal) {
                    progressModal.close();
                }

                if (window.toast) {
                    window.toast.warning('NFO同步监控超时，请手动检查同步结果');
                }
            }
        }, 30 * 60 * 1000);
    }

    /**
     * 更新批量NFO同步进度
     * @param {number} current - 当前进度
     * @param {number} total - 总数
     * @param {string} currentMovie - 当前处理的影片
     */
    updateBatchNfoProgress(current, total, currentMovie) {
        const progressBar = document.getElementById('batch-nfo-progress');
        const progressText = document.getElementById('batch-nfo-progress-text');
        const currentMovieEl = document.getElementById('batch-nfo-current-movie');

        if (progressBar) {
            const percentage = total > 0 ? Math.round((current / total) * 100) : 0;
            progressBar.value = percentage;
        }

        if (progressText) {
            progressText.textContent = `${current} / ${total}`;
        }

        if (currentMovieEl) {
            currentMovieEl.textContent = currentMovie;
        }
    }

    /**
     * 从进度数据显示批量NFO同步结果
     * @param {Object} progress - 进度数据
     */
    showBatchNfoSyncResultFromProgress(progress) {
        const resultData = {
            success: progress.status === 'completed',
            message: progress.status === 'completed' ? '批量NFO同步完成' :
                     progress.status === 'cancelled' ? '批量NFO同步已取消' : '批量NFO同步失败',
            data: {
                total_count: progress.total_count || 0,
                success_count: progress.success_count || 0,
                failed_count: progress.failed_count || 0,
                errors: progress.errors || []
            }
        };

        this.showBatchNfoSyncResult(resultData);
    }

    /**
     * 取消批量NFO同步
     */
    async cancelBatchNfoSync() {
        if (!this.nfoSyncTaskId) {
            return;
        }

        try {
            const response = await api.cancelNfoSync(this.nfoSyncTaskId);

            if (response.success) {
                // 停止进度监控
                if (this.nfoSyncProgressInterval) {
                    clearInterval(this.nfoSyncProgressInterval);
                    this.nfoSyncProgressInterval = null;
                }

                // 关闭进度对话框
                const progressModal = document.getElementById('batch-nfo-sync-progress-modal');
                if (progressModal) {
                    progressModal.close();
                }

                if (window.toast) {
                    window.toast.info('NFO同步任务已取消');
                }

                this.nfoSyncTaskId = null;
            } else {
                throw new Error(response.message || '取消任务失败');
            }

        } catch (error) {
            console.error('取消NFO同步任务失败:', error);
            if (window.toast) {
                window.toast.error(`取消任务失败: ${error.message}`);
            }
        }
    }

    /**
     * 显示批量NFO同步结果
     * @param {Object} result - 同步结果
     */
    showBatchNfoSyncResult(result) {
        const modal = document.getElementById('batch-nfo-sync-result-modal');
        const content = document.getElementById('batch-nfo-result-content');

        if (!modal || !content) return;

        if (result.success) {
            const data = result.data || {};
            const totalCount = data.total_count || 0;
            const successCount = data.success_count || 0;
            const failedCount = data.failed_count || 0;
            const errors = data.errors || [];

            let html = `
                <div class="alert alert-success mb-4">
                    <i class="bi bi-check-circle" aria-label="成功图标"></i>
                    <div>
                        <div class="font-medium">${result.message}</div>
                        <div class="text-sm mt-1">
                            总计: ${totalCount} 部影片，成功: ${successCount} 部，失败: ${failedCount} 部
                        </div>
                    </div>
                </div>
            `;

            if (successCount > 0) {
                html += `
                    <div class="mb-4">
                        <div class="text-sm font-medium text-success mb-2">
                            <i class="bi bi-check-circle mr-1" aria-label="成功图标"></i>
                            成功同步的影片已自动清理更新标记
                        </div>
                    </div>
                `;
            }

            if (failedCount > 0 && errors.length > 0) {
                html += `
                    <div class="mb-4">
                        <div class="text-sm font-medium text-error mb-2">
                            <i class="bi bi-exclamation-triangle mr-1" aria-label="错误图标"></i>
                            同步失败的影片:
                        </div>
                        <div class="bg-base-200 rounded p-3 max-h-32 overflow-y-auto">
                            <ul class="text-xs space-y-1">
                                ${errors.map(error => `<li>• ${this.escapeHtml(error)}</li>`).join('')}
                            </ul>
                        </div>
                    </div>
                `;
            }

            content.innerHTML = html;
        } else {
            content.innerHTML = `
                <div class="alert alert-error">
                    <i class="bi bi-exclamation-triangle" aria-label="错误图标"></i>
                    <div>
                        <div class="font-medium">同步失败</div>
                        <div class="text-sm mt-1">${this.escapeHtml(result.message)}</div>
                    </div>
                </div>
            `;
        }

        modal.showModal();
    }

    /**
     * 转义HTML特殊字符
     * @param {string} text - 要转义的文本
     * @returns {string} 转义后的文本
     */
    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }






}

// 页面加载完成后初始化影片库
document.addEventListener('DOMContentLoaded', () => {
    if (window.location.pathname === '/movies') {
        const moviesPage = new MoviesPage();
        moviesPage.init();

        // 将实例暴露到全局，方便调试
        window.moviesPage = moviesPage;
    }
});
