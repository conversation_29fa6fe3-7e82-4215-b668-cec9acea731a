"""
映射规则服务
用于在数据导入过程中应用映射规则，转换或过滤原始值
"""
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from app.models.models import MappingRule
import logging
import re
import time
import threading
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class MappingService:
    """映射规则服务类"""

    # 类级别的缓存，所有实例共享
    _global_mapping_cache = {}
    _global_cache_loaded = False
    _cache_last_updated = None
    _cache_lock = threading.RLock()  # 使用可重入锁
    _cache_ttl = 3600  # 缓存生存时间（秒），默认1小时

    def __init__(self, db: Session):
        self.db = db

    @staticmethod
    def normalize_string(value: str) -> str:
        """
        标准化字符串：去除前后空格，规范化中间空格

        Args:
            value: 原始字符串

        Returns:
            标准化后的字符串
        """
        if not value:
            return ""

        # 去除前后空格
        normalized = value.strip()

        # 将多个连续空格替换为单个空格
        normalized = re.sub(r'\s+', ' ', normalized)

        return normalized
    
    def _is_cache_expired(self) -> bool:
        """检查缓存是否过期"""
        if not MappingService._cache_last_updated:
            return True

        expiry_time = MappingService._cache_last_updated + timedelta(seconds=MappingService._cache_ttl)
        return datetime.now() > expiry_time

    def _load_mapping_cache(self, force_reload: bool = False):
        """
        加载所有映射规则到缓存

        Args:
            force_reload: 是否强制重新加载缓存
        """
        with MappingService._cache_lock:
            # 检查是否需要加载缓存
            if not force_reload and MappingService._global_cache_loaded and not self._is_cache_expired():
                return

            try:
                start_time = time.time()
                all_rules = self.db.query(MappingRule).all()

                # 使用临时字典构建缓存，避免在构建过程中影响正在使用的缓存
                temp_cache = {}

                for rule in all_rules:
                    if rule.type not in temp_cache:
                        temp_cache[rule.type] = {}

                    # 标准化原始值和映射值
                    normalized_original = self.normalize_string(rule.original_value)
                    normalized_mapped = self.normalize_string(rule.mapped_value) if rule.mapped_value else None

                    if normalized_original:  # 只有标准化后的原始值不为空才添加
                        temp_cache[rule.type][normalized_original] = normalized_mapped

                # 原子性地更新缓存
                MappingService._global_mapping_cache = temp_cache
                MappingService._global_cache_loaded = True
                MappingService._cache_last_updated = datetime.now()

                load_time = time.time() - start_time
                logger.info(f"映射规则缓存加载完成，共 {len(all_rules)} 条规则，耗时 {load_time:.3f}s")

            except Exception as e:
                logger.error(f"加载映射规则缓存失败: {e}")
                # 保持现有缓存，不清空
                if not MappingService._global_mapping_cache:
                    MappingService._global_mapping_cache = {}
    
    def apply_mapping_rules(self, mapping_type: str, original_values: List[str]) -> List[str]:
        """
        应用映射规则到原始值列表

        Args:
            mapping_type: 映射类型 (tags/genres/series/actors)
            original_values: 原始值列表

        Returns:
            应用映射规则后的值列表（已过滤删除规则）
        """
        if not original_values:
            return []

        # 确保缓存已加载（带过期检查）
        self._load_mapping_cache()

        mapped_values = []
        # 使用线程安全的方式获取缓存
        with MappingService._cache_lock:
            type_mappings = MappingService._global_mapping_cache.get(mapping_type, {})
        
        for original_value in original_values:
            if not original_value:
                continue

            # 标准化原始值
            normalized_original = self.normalize_string(original_value)
            if not normalized_original:
                continue

            logger.debug(f"处理 {mapping_type} 映射: 原始值='{original_value}' 标准化='{normalized_original}'")

            # 检查是否有映射规则
            if normalized_original in type_mappings:
                mapped_value = type_mappings[normalized_original]

                # 如果映射值为空或None，表示删除规则，跳过此值
                if mapped_value:
                    normalized_mapped = self.normalize_string(mapped_value)
                    if normalized_mapped:
                        mapped_values.append(normalized_mapped)
                        logger.debug(f"应用映射规则 [{mapping_type}]: '{normalized_original}' -> '{normalized_mapped}'")
                    else:
                        logger.debug(f"应用删除规则 [{mapping_type}]: '{normalized_original}' -> 删除（映射值为空）")
                else:
                    logger.debug(f"应用删除规则 [{mapping_type}]: '{normalized_original}' -> 删除")
            else:
                # 没有映射规则，保持标准化后的原值
                mapped_values.append(normalized_original)
                logger.debug(f"无映射规则 [{mapping_type}]: '{normalized_original}' -> 保持原值")
        
        return mapped_values
    
    def apply_tag_mappings(self, tag_names: List[str]) -> List[str]:
        """应用标签映射规则"""
        return self.apply_mapping_rules('tags', tag_names)
    
    def apply_genre_mappings(self, genre_names: List[str]) -> List[str]:
        """应用分类映射规则"""
        return self.apply_mapping_rules('genres', genre_names)

    def apply_series_mappings(self, series_names: List[str]) -> List[str]:
        """应用系列映射规则"""
        return self.apply_mapping_rules('series', series_names)

    def apply_actor_mappings(self, actor_names: List[str]) -> List[str]:
        """
        应用演员映射规则
        
        Args:
            actor_names: 演员姓名列表
            
        Returns:
            应用映射规则后的演员姓名列表
        """
        return self.apply_mapping_rules('actors', actor_names)
    
    def apply_actor_data_mappings(self, actors_data: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """
        应用演员数据映射规则

        Args:
            actors_data: 演员数据列表，每个元素包含 name, role, actor_type 等字段

        Returns:
            应用映射规则后的演员数据列表
        """
        if not actors_data:
            return []

        logger.debug(f"开始处理 {len(actors_data)} 个演员的映射规则")

        # 确保缓存已加载（带过期检查）
        self._load_mapping_cache()

        # 使用线程安全的方式获取缓存
        with MappingService._cache_lock:
            type_mappings = MappingService._global_mapping_cache.get('actors', {})

        filtered_actors = []
        for actor in actors_data:
            original_name = actor.get('name', '')
            if not original_name:
                continue

            # 标准化演员姓名
            normalized_name = self.normalize_string(original_name)
            if not normalized_name:
                continue

            logger.debug(f"处理演员: 原始='{original_name}' 标准化='{normalized_name}'")

            # 检查是否有映射规则
            if normalized_name in type_mappings:
                mapped_value = type_mappings[normalized_name]

                # 如果映射值为空或None，表示删除规则，跳过此演员
                if mapped_value:
                    normalized_mapped = self.normalize_string(mapped_value)
                    if normalized_mapped:
                        # 创建副本并更新演员姓名
                        actor_copy = actor.copy()
                        actor_copy['name'] = normalized_mapped
                        filtered_actors.append(actor_copy)
                        logger.debug(f"演员映射: '{normalized_name}' -> '{normalized_mapped}'")
                    else:
                        logger.debug(f"演员删除规则: '{normalized_name}' -> 删除（映射值为空）")
                else:
                    logger.debug(f"演员删除规则: '{normalized_name}' -> 删除")
            else:
                # 没有映射规则，保持标准化后的原值
                actor_copy = actor.copy()
                actor_copy['name'] = normalized_name
                filtered_actors.append(actor_copy)
                logger.debug(f"演员无映射: '{normalized_name}' -> 保持原值")

        logger.debug(f"演员映射完成: {len(actors_data)} -> {len(filtered_actors)} 个演员")
        return filtered_actors
    
    def _get_mapped_value(self, mapping_type: str, original_value: str) -> Optional[str]:
        """获取单个值的映射结果"""
        self._load_mapping_cache()
        with MappingService._cache_lock:
            type_mappings = MappingService._global_mapping_cache.get(mapping_type, {})
            return type_mappings.get(original_value, original_value)

    def refresh_cache(self):
        """刷新映射规则缓存"""
        logger.info("手动刷新映射规则缓存")
        self._load_mapping_cache(force_reload=True)

    @classmethod
    def invalidate_cache(cls):
        """使缓存失效（类方法，可以在任何地方调用）"""
        with cls._cache_lock:
            cls._global_cache_loaded = False
            cls._cache_last_updated = None
            # 不立即清空缓存，保留现有数据直到下次加载
            logger.info("映射规则缓存已标记为失效")

    @classmethod
    def set_cache_ttl(cls, ttl_seconds: int):
        """
        设置缓存生存时间

        Args:
            ttl_seconds: 缓存生存时间（秒）
        """
        cls._cache_ttl = ttl_seconds
        logger.info(f"映射规则缓存TTL已设置为 {ttl_seconds} 秒")

    @classmethod
    def get_cache_info(cls) -> Dict[str, Any]:
        """获取缓存信息"""
        with cls._cache_lock:
            return {
                'loaded': cls._global_cache_loaded,
                'last_updated': cls._cache_last_updated.isoformat() if cls._cache_last_updated else None,
                'ttl_seconds': cls._cache_ttl,
                'expired': cls._cache_last_updated and
                          (datetime.now() > cls._cache_last_updated + timedelta(seconds=cls._cache_ttl)),
                'cache_size': len(cls._global_mapping_cache),
                'types': list(cls._global_mapping_cache.keys()) if cls._global_mapping_cache else []
            }


