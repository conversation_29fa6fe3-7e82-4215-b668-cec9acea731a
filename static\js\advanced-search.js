/**
 * 高级搜索功能管理器
 */
class AdvancedSearchManager {
    constructor() {
        this.modal = null;
        this.tags = [];
        this.genres = [];
        this.series = [];
        this.years = [];
        this.selectedFilters = {
            tags: [],
            genres: [],
            series: [],
            yearFrom: '',
            yearTo: '',
            ratingFrom: '',
            ratingTo: '',
            isUpdated: ''
        };

        // 多选下拉框实例
        this.tagsSelector = null;
        this.genresSelector = null;
        this.seriesSelector = null;

        this.init();
    }

    /**
     * 初始化高级搜索
     */
    init() {
        this.modal = document.getElementById('advanced-search-modal');
        this.initMultiSelectDropdowns();
        this.bindEvents();
        this.loadFilterData();
    }

    /**
     * 初始化多选下拉框
     */
    initMultiSelectDropdowns() {
        // 初始化标签选择器
        this.tagsSelector = new MultiSelectDropdown('tags-selector', {
            placeholder: '选择标签...',
            searchPlaceholder: '搜索标签...',
            noDataText: '暂无标签',
            noResultsText: '未找到匹配的标签',
            maxDisplayTags: 3,
            allowClear: true
        });

        // 初始化分类选择器
        this.genresSelector = new MultiSelectDropdown('genres-selector', {
            placeholder: '选择分类...',
            searchPlaceholder: '搜索分类...',
            noDataText: '暂无分类',
            noResultsText: '未找到匹配的分类',
            maxDisplayTags: 3,
            allowClear: true
        });

        // 初始化系列选择器
        this.seriesSelector = new MultiSelectDropdown('series-selector', {
            placeholder: '选择系列...',
            searchPlaceholder: '搜索系列...',
            noDataText: '暂无系列',
            noResultsText: '未找到匹配的系列',
            maxDisplayTags: 3,
            allowClear: true
        });

        // 绑定选择变化事件
        this.tagsSelector.onChange = (selectedItems) => {
            this.selectedFilters.tags = Array.from(selectedItems.keys()).map(id => parseInt(id));
        };

        this.genresSelector.onChange = (selectedItems) => {
            this.selectedFilters.genres = Array.from(selectedItems.keys()).map(id => parseInt(id));
        };

        this.seriesSelector.onChange = (selectedItems) => {
            this.selectedFilters.series = Array.from(selectedItems.keys()).map(id => parseInt(id));
        };
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 高级搜索按钮
        const advancedSearchBtn = document.getElementById('advanced-search-btn');
        if (advancedSearchBtn) {
            advancedSearchBtn.addEventListener('click', () => this.openModal());
        }

        // 应用筛选按钮
        const applyBtn = document.getElementById('apply-advanced-search');
        if (applyBtn) {
            applyBtn.addEventListener('click', () => this.applyFilters());
        }

        // 重置按钮
        const resetBtn = document.getElementById('reset-advanced-search');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => this.resetFilters());
        }
    }



    /**
     * 加载过滤器数据
     */
    async loadFilterData() {
        try {
            // 加载标签
            const tagsData = await api.getTags();
            this.tags = tagsData.data || [];
            this.tagsSelector.setData(this.tags);

            // 加载分类
            const genresData = await api.getGenres();
            this.genres = genresData.data || [];
            this.genresSelector.setData(this.genres);

            // 加载系列
            const seriesData = await api.getSeries();
            this.series = seriesData.data || [];
            this.seriesSelector.setData(this.series);

            // 年份输入框已改为数字输入，无需生成选项
            // 添加年份输入框的验证事件
            this.setupYearValidation();

        } catch (error) {
            console.error('加载过滤器数据失败:', error);
        }
    }







    /**
     * 打开模态框
     */
    openModal() {
        if (this.modal) {
            this.modal.showModal();
        }
    }

    /**
     * 设置年份输入框验证
     */
    setupYearValidation() {
        const yearFromInput = document.getElementById('year-from');
        const yearToInput = document.getElementById('year-to');

        if (yearFromInput && yearToInput) {
            // 为年份输入框添加输入事件监听器
            [yearFromInput, yearToInput].forEach(input => {
                input.addEventListener('input', () => {
                    this.validateYearRange();
                });

                input.addEventListener('blur', () => {
                    this.validateYearRange();
                });
            });
        }
    }

    /**
     * 验证年份范围
     */
    validateYearRange() {
        const yearFromInput = document.getElementById('year-from');
        const yearToInput = document.getElementById('year-to');

        if (!yearFromInput || !yearToInput) return;

        const yearFrom = yearFromInput.value;
        const yearTo = yearToInput.value;

        // 清除之前的错误状态
        yearFromInput.classList.remove('input-error');
        yearToInput.classList.remove('input-error');

        // 如果两个值都存在，进行范围验证
        if (yearFrom && yearTo) {
            const yearFromNum = parseInt(yearFrom);
            const yearToNum = parseInt(yearTo);

            if (yearFromNum > yearToNum) {
                yearFromInput.classList.add('input-error');
                yearToInput.classList.add('input-error');
            }
        }
    }

    /**
     * 应用筛选
     */
    applyFilters() {
        // 获取多选下拉框的选中值
        this.selectedFilters.tags = this.tagsSelector ? this.tagsSelector.getSelectedValues() : [];
        this.selectedFilters.genres = this.genresSelector ? this.genresSelector.getSelectedValues() : [];
        this.selectedFilters.series = this.seriesSelector ? this.seriesSelector.getSelectedValues() : [];

        // 获取年份和评分范围
        this.selectedFilters.yearFrom = document.getElementById('year-from')?.value || '';
        this.selectedFilters.yearTo = document.getElementById('year-to')?.value || '';
        this.selectedFilters.ratingFrom = document.getElementById('rating-from')?.value || '';
        this.selectedFilters.ratingTo = document.getElementById('rating-to')?.value || '';

        // 获取更新状态
        this.selectedFilters.isUpdated = document.getElementById('is-updated')?.value || '';

        // 验证年份范围
        if (this.selectedFilters.yearFrom && this.selectedFilters.yearTo) {
            const yearFrom = parseInt(this.selectedFilters.yearFrom);
            const yearTo = parseInt(this.selectedFilters.yearTo);

            if (yearFrom > yearTo) {
                alert('起始年份不能大于结束年份');
                return;
            }
        }

        // 触发自定义事件，通知主搜索组件
        const event = new CustomEvent('advancedSearchApplied', {
            detail: this.selectedFilters
        });
        document.dispatchEvent(event);

        // 关闭模态框
        this.modal.close();
    }

    /**
     * 重置筛选
     */
    resetFilters() {
        // 重置选择状态
        this.selectedFilters = {
            tags: [],
            genres: [],
            series: [],
            yearFrom: '',
            yearTo: '',
            ratingFrom: '',
            ratingTo: '',
            isUpdated: ''
        };

        // 重置多选下拉框
        this.tagsSelector.clear();
        this.genresSelector.clear();
        this.seriesSelector.clear();

        // 重置输入框
        const yearFromInput = document.getElementById('year-from');
        const yearToInput = document.getElementById('year-to');
        const ratingFromInput = document.getElementById('rating-from');
        const ratingToInput = document.getElementById('rating-to');
        const isUpdatedInput = document.getElementById('is-updated');

        if (yearFromInput) {
            yearFromInput.value = '';
            yearFromInput.classList.remove('input-error');
        }
        if (yearToInput) {
            yearToInput.value = '';
            yearToInput.classList.remove('input-error');
        }
        if (ratingFromInput) ratingFromInput.value = '';
        if (ratingToInput) ratingToInput.value = '';
        if (isUpdatedInput) isUpdatedInput.value = '';
    }

    /**
     * 获取当前筛选条件
     */
    getCurrentFilters() {
        return { ...this.selectedFilters };
    }
}

// 全局实例
window.advancedSearch = new AdvancedSearchManager();
