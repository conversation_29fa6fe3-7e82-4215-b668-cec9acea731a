"""
NFO同步进度管理服务
提供NFO批量同步的进度追踪和状态管理功能
"""
import uuid
import threading
from datetime import datetime
from typing import Dict, List, Any, Optional
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class NFOSyncStatus(Enum):
    """NFO同步状态枚举"""
    IDLE = "idle"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    ERROR = "error"


class NFOSyncProgressService:
    """NFO同步进度管理服务"""
    
    def __init__(self):
        self.sync_tasks: Dict[str, Dict[str, Any]] = {}
        self.lock = threading.Lock()
        
    def create_sync_task(self, movie_ids: List[int], task_type: str = "批量NFO同步") -> str:
        """
        创建NFO同步任务
        
        Args:
            movie_ids: 要同步的影片ID列表
            task_type: 任务类型
            
        Returns:
            任务ID
        """
        task_id = str(uuid.uuid4())
        
        with self.lock:
            self.sync_tasks[task_id] = {
                "task_id": task_id,
                "status": NFOSyncStatus.IDLE,
                "task_type": task_type,
                "movie_ids": movie_ids,
                "total_count": len(movie_ids),
                "current_index": 0,
                "current_movie_title": "",
                "current_movie_id": None,
                "success_count": 0,
                "failed_count": 0,
                "success_movie_ids": [],
                "failed_movie_ids": [],
                "errors": [],
                "start_time": None,
                "end_time": None,
                "cancel_requested": False
            }
        
        logger.info(f"创建NFO同步任务: {task_id}, 影片数量: {len(movie_ids)}")
        return task_id
    
    def start_sync_task(self, task_id: str) -> bool:
        """
        开始NFO同步任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否成功开始
        """
        with self.lock:
            if task_id not in self.sync_tasks:
                return False
            
            task = self.sync_tasks[task_id]
            if task["status"] != NFOSyncStatus.IDLE:
                return False
            
            task["status"] = NFOSyncStatus.RUNNING
            task["start_time"] = datetime.now()
            task["cancel_requested"] = False
        
        logger.info(f"开始NFO同步任务: {task_id}")
        return True
    
    def update_sync_progress(self, task_id: str, **kwargs) -> bool:
        """
        更新NFO同步进度
        
        Args:
            task_id: 任务ID
            **kwargs: 更新的字段
            
        Returns:
            是否成功更新
        """
        with self.lock:
            if task_id not in self.sync_tasks:
                return False
            
            task = self.sync_tasks[task_id]
            
            # 更新字段
            for key, value in kwargs.items():
                if key in task:
                    task[key] = value
        
        return True
    
    def get_sync_progress(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取NFO同步进度
        
        Args:
            task_id: 任务ID
            
        Returns:
            进度信息字典
        """
        with self.lock:
            if task_id not in self.sync_tasks:
                return None
            
            task = self.sync_tasks[task_id].copy()
            
            # 计算进度百分比
            if task["total_count"] > 0:
                progress_percentage = round((task["current_index"] / task["total_count"]) * 100, 1)
            else:
                progress_percentage = 0
            
            task["progress_percentage"] = progress_percentage
            
            # 计算已用时间
            if task["start_time"]:
                if task["end_time"]:
                    elapsed_time = (task["end_time"] - task["start_time"]).total_seconds()
                else:
                    elapsed_time = (datetime.now() - task["start_time"]).total_seconds()
                task["elapsed_time"] = int(elapsed_time)
            else:
                task["elapsed_time"] = 0
            
            return task
    
    def complete_sync_task(self, task_id: str, success: bool = True, error_message: str = None) -> bool:
        """
        完成NFO同步任务
        
        Args:
            task_id: 任务ID
            success: 是否成功完成
            error_message: 错误消息
            
        Returns:
            是否成功完成
        """
        with self.lock:
            if task_id not in self.sync_tasks:
                return False
            
            task = self.sync_tasks[task_id]
            task["end_time"] = datetime.now()
            
            if success:
                task["status"] = NFOSyncStatus.COMPLETED
            else:
                task["status"] = NFOSyncStatus.ERROR
                if error_message:
                    task["errors"].append(error_message)
        
        logger.info(f"完成NFO同步任务: {task_id}, 成功: {success}")
        return True
    
    def cancel_sync_task(self, task_id: str) -> bool:
        """
        取消NFO同步任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否成功取消
        """
        with self.lock:
            if task_id not in self.sync_tasks:
                return False
            
            task = self.sync_tasks[task_id]
            task["cancel_requested"] = True
            
            if task["status"] == NFOSyncStatus.RUNNING:
                task["status"] = NFOSyncStatus.CANCELLED
                task["end_time"] = datetime.now()
        
        logger.info(f"取消NFO同步任务: {task_id}")
        return True
    
    def is_cancel_requested(self, task_id: str) -> bool:
        """
        检查是否请求取消
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否请求取消
        """
        with self.lock:
            if task_id not in self.sync_tasks:
                return False
            
            return self.sync_tasks[task_id]["cancel_requested"]
    
    def remove_sync_task(self, task_id: str) -> bool:
        """
        移除NFO同步任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否成功移除
        """
        with self.lock:
            if task_id in self.sync_tasks:
                del self.sync_tasks[task_id]
                logger.info(f"移除NFO同步任务: {task_id}")
                return True
            return False
    
    def get_all_tasks(self) -> List[Dict[str, Any]]:
        """
        获取所有NFO同步任务
        
        Returns:
            任务列表
        """
        with self.lock:
            return [self.get_sync_progress(task_id) for task_id in self.sync_tasks.keys()]
    
    def cleanup_completed_tasks(self, max_age_hours: int = 24) -> int:
        """
        清理已完成的任务
        
        Args:
            max_age_hours: 最大保留时间（小时）
            
        Returns:
            清理的任务数量
        """
        current_time = datetime.now()
        cleaned_count = 0
        
        with self.lock:
            task_ids_to_remove = []
            
            for task_id, task in self.sync_tasks.items():
                if task["status"] in [NFOSyncStatus.COMPLETED, NFOSyncStatus.CANCELLED, NFOSyncStatus.ERROR]:
                    if task["end_time"]:
                        age_hours = (current_time - task["end_time"]).total_seconds() / 3600
                        if age_hours > max_age_hours:
                            task_ids_to_remove.append(task_id)
            
            for task_id in task_ids_to_remove:
                del self.sync_tasks[task_id]
                cleaned_count += 1
        
        if cleaned_count > 0:
            logger.info(f"清理了 {cleaned_count} 个已完成的NFO同步任务")
        
        return cleaned_count


# 全局NFO同步进度服务实例
nfo_sync_progress_service = NFOSyncProgressService()
