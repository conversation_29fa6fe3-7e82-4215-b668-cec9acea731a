"""
映射规则管理 API
提供映射规则的 CRUD 操作和批量处理功能
"""
from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Response
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from sqlalchemy import and_, or_

from app.core.database import get_db
from app.models.models import MappingRule
from app.services.mapping_service import MappingService
from app.schemas.schemas import (
    BaseResponse,
    MappingRuleCreate,
    MappingRuleUpdate,
    MappingRuleResponse,
    MappingRuleBatchCreate,
    MappingRuleBatchResponse,
    MappingRuleListResponse,
    MappingRuleListRequest,
    MappingRuleExportResponse,
    MappingRuleDeleteRequest
)

# 创建路由器
router = APIRouter(
    prefix="/mappings",
    tags=["映射管理"],
    responses={404: {"description": "映射规则不存在"}}
)

# 支持的映射类型
SUPPORTED_TYPES = ["tags", "genres", "series", "actors"]


def validate_mapping_type(mapping_type: str):
    """验证映射类型"""
    if mapping_type not in SUPPORTED_TYPES:
        raise HTTPException(
            status_code=400,
            detail=f"不支持的映射类型: {mapping_type}。支持的类型: {', '.join(SUPPORTED_TYPES)}"
        )


@router.post("/{mapping_type}/list", response_model=MappingRuleListResponse)
async def get_mapping_rules(
    mapping_type: str,
    request: MappingRuleListRequest,
    db: Session = Depends(get_db)
):
    """获取指定类型的映射规则列表"""
    validate_mapping_type(mapping_type)

    # 构建查询
    query = db.query(MappingRule).filter(MappingRule.type == mapping_type)

    # 状态过滤
    if request.status:
        query = query.filter(MappingRule.status == request.status)

    # 搜索过滤
    if request.search:
        search_term = f"%{request.search}%"
        query = query.filter(
            or_(
                MappingRule.original_value.ilike(search_term),
                MappingRule.mapped_value.ilike(search_term)
            )
        )

    # 获取总数
    total_count = query.count()

    # 分页查询
    rules = query.order_by(MappingRule.created_at.desc()).offset(request.offset).limit(request.limit).all()

    return MappingRuleListResponse(
        success=True,
        message="获取映射规则列表成功",
        data=rules,
        total_count=total_count,
        limit=request.limit,
        offset=request.offset
    )


@router.post("/{mapping_type}/add", response_model=BaseResponse)
async def create_mapping_rule(
    mapping_type: str,
    rule_data: MappingRuleCreate,
    db: Session = Depends(get_db)
):
    """创建新的映射规则"""
    validate_mapping_type(mapping_type)
    
    try:
        # 标准化输入值
        normalized_original = MappingService.normalize_string(rule_data.original_value)
        normalized_mapped = MappingService.normalize_string(rule_data.mapped_value) if rule_data.mapped_value else None

        if not normalized_original:
            raise HTTPException(status_code=400, detail="原始值不能为空或只包含空格")

        # 验证原始值和映射值不能相同
        if normalized_mapped and normalized_original == normalized_mapped:
            raise HTTPException(status_code=400, detail="原始值和映射值不能相同")

        # 确定状态
        status = "empty" if not normalized_mapped else "active"

        # 创建映射规则
        mapping_rule = MappingRule(
            type=mapping_type,
            original_value=normalized_original,
            mapped_value=normalized_mapped,
            status=status
        )
        
        db.add(mapping_rule)
        db.commit()
        db.refresh(mapping_rule)

        # 使映射规则缓存失效
        MappingService.invalidate_cache()

        return BaseResponse(
            success=True,
            message="映射规则创建成功",
            data=MappingRuleResponse.from_orm(mapping_rule)
        )
        
    except IntegrityError:
        db.rollback()
        raise HTTPException(
            status_code=400,
            detail=f"原始值 '{rule_data.original_value}' 在 {mapping_type} 类型中已存在"
        )
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"创建映射规则失败: {str(e)}")


@router.put("/{mapping_type}/edit/{rule_id}", response_model=BaseResponse)
async def update_mapping_rule(
    mapping_type: str,
    rule_id: int,
    rule_data: MappingRuleUpdate,
    db: Session = Depends(get_db)
):
    """更新指定的映射规则"""
    validate_mapping_type(mapping_type)
    
    # 查找映射规则
    mapping_rule = db.query(MappingRule).filter(
        and_(MappingRule.id == rule_id, MappingRule.type == mapping_type)
    ).first()
    
    if not mapping_rule:
        raise HTTPException(status_code=404, detail="映射规则不存在")
    
    try:
        # 更新字段
        if rule_data.original_value is not None:
            normalized_original = MappingService.normalize_string(rule_data.original_value)
            if not normalized_original:
                raise HTTPException(status_code=400, detail="原始值不能为空或只包含空格")
            mapping_rule.original_value = normalized_original

        if rule_data.mapped_value is not None:
            normalized_mapped = MappingService.normalize_string(rule_data.mapped_value) if rule_data.mapped_value else None
            mapping_rule.mapped_value = normalized_mapped
            mapping_rule.status = "empty" if not normalized_mapped else "active"

        # 验证原始值和映射值不能相同
        if mapping_rule.mapped_value and mapping_rule.original_value == mapping_rule.mapped_value:
            raise HTTPException(status_code=400, detail="原始值和映射值不能相同")

        mapping_rule.updated_at = datetime.utcnow()

        db.commit()
        db.refresh(mapping_rule)

        # 使映射规则缓存失效
        MappingService.invalidate_cache()

        return BaseResponse(
            success=True,
            message="映射规则更新成功",
            data=MappingRuleResponse.from_orm(mapping_rule)
        )
        
    except IntegrityError:
        db.rollback()
        raise HTTPException(
            status_code=400,
            detail=f"原始值 '{rule_data.original_value}' 在 {mapping_type} 类型中已存在"
        )
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"更新映射规则失败: {str(e)}")


@router.delete("/{mapping_type}", response_model=BaseResponse)
async def delete_mapping_rules(
    mapping_type: str,
    delete_request: MappingRuleDeleteRequest,
    db: Session = Depends(get_db)
):
    """批量删除映射规则"""
    validate_mapping_type(mapping_type)

    success_count = 0
    failed_count = 0
    errors = []

    try:
        for rule_id in delete_request.mappings_ids:
            try:
                # 查找映射规则
                mapping_rule = db.query(MappingRule).filter(
                    and_(MappingRule.id == rule_id, MappingRule.type == mapping_type)
                ).first()

                if not mapping_rule:
                    failed_count += 1
                    errors.append(f"映射规则 ID {rule_id} 不存在")
                    continue

                db.delete(mapping_rule)
                success_count += 1

            except Exception as e:
                failed_count += 1
                errors.append(f"删除映射规则 ID {rule_id} 失败: {str(e)}")

        db.commit()

        # 使映射规则缓存失效
        MappingService.invalidate_cache()

        if failed_count == 0:
            return BaseResponse(
                success=True,
                message=f"成功删除 {success_count} 个映射规则"
            )
        else:
            return BaseResponse(
                success=success_count > 0,
                message=f"删除完成：成功 {success_count} 个，失败 {failed_count} 个",
                data={
                    "success_count": success_count,
                    "failed_count": failed_count,
                    "errors": errors
                }
            )

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"批量删除映射规则失败: {str(e)}")


@router.post("/{mapping_type}/import", response_model=MappingRuleBatchResponse)
async def import_mapping_rules(
    mapping_type: str,
    batch_data: MappingRuleBatchCreate,
    db: Session = Depends(get_db)
):
    """导入映射规则"""
    validate_mapping_type(mapping_type)
    
    success_rules = []
    failed_rules = []
    errors = []
    
    for rule_data in batch_data.rules:
        try:
            # 标准化输入值
            normalized_original = MappingService.normalize_string(rule_data.original_value)
            normalized_mapped = MappingService.normalize_string(rule_data.mapped_value) if rule_data.mapped_value else None

            if not normalized_original:
                failed_rules.append({
                    "original_value": rule_data.original_value,
                    "mapped_value": rule_data.mapped_value,
                    "error": "原始值不能为空或只包含空格"
                })
                errors.append(f"原始值 '{rule_data.original_value}' 不能为空或只包含空格")
                continue

            # 检查是否已存在
            existing_rule = db.query(MappingRule).filter(
                and_(
                    MappingRule.type == mapping_type,
                    MappingRule.original_value == normalized_original
                )
            ).first()

            if existing_rule:
                if batch_data.overwrite:
                    # 覆盖现有规则
                    existing_rule.mapped_value = normalized_mapped
                    existing_rule.status = "empty" if not normalized_mapped else "active"
                    existing_rule.updated_at = datetime.utcnow()
                    db.commit()
                    db.refresh(existing_rule)
                    success_rules.append(MappingRuleResponse.from_orm(existing_rule))
                else:
                    # 跳过已存在的规则
                    failed_rules.append({
                        "original_value": rule_data.original_value,
                        "mapped_value": rule_data.mapped_value,
                        "error": "原始值已存在"
                    })
                    errors.append(f"原始值 '{normalized_original}' 已存在")
            else:
                # 创建新规则
                status = "empty" if not normalized_mapped else "active"
                mapping_rule = MappingRule(
                    type=mapping_type,
                    original_value=normalized_original,
                    mapped_value=normalized_mapped,
                    status=status
                )
                
                db.add(mapping_rule)
                db.commit()
                db.refresh(mapping_rule)
                success_rules.append(MappingRuleResponse.from_orm(mapping_rule))
                
        except Exception as e:
            db.rollback()
            failed_rules.append({
                "original_value": rule_data.original_value,
                "mapped_value": rule_data.mapped_value,
                "error": str(e)
            })
            errors.append(f"处理 '{rule_data.original_value}' 时出错: {str(e)}")

    # 如果有成功的操作，使映射规则缓存失效
    if success_rules:
        MappingService.invalidate_cache()

    return MappingRuleBatchResponse(
        success_count=len(success_rules),
        failed_count=len(failed_rules),
        success_rules=success_rules,
        failed_rules=failed_rules,
        errors=errors
    )


@router.get("/{mapping_type}/export", response_model=MappingRuleExportResponse)
async def export_mapping_rules(
    mapping_type: str,
    db: Session = Depends(get_db)
):
    """导出映射规则为 JSON"""
    validate_mapping_type(mapping_type)
    
    # 获取所有映射规则
    rules = db.query(MappingRule).filter(MappingRule.type == mapping_type).all()
    
    # 转换为导出格式
    export_rules = [
        {
            "original": rule.original_value,
            "mapped": rule.mapped_value
        }
        for rule in rules
    ]
    
    return MappingRuleExportResponse(
        type=mapping_type,
        rules=export_rules,
        exported_at=datetime.utcnow(),
        total_count=len(export_rules)
    )


@router.get("/cache/info", response_model=BaseResponse)
async def get_cache_info():
    """获取映射规则缓存信息"""
    try:
        cache_info = MappingService.get_cache_info()
        return BaseResponse(
            success=True,
            message="获取缓存信息成功",
            data=cache_info
        )
    except Exception as e:
        logger.error(f"获取缓存信息时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取缓存信息失败: {str(e)}")


@router.post("/cache/refresh", response_model=BaseResponse)
async def refresh_cache(db: Session = Depends(get_db)):
    """手动刷新映射规则缓存"""
    try:
        mapping_service = MappingService(db)
        mapping_service.refresh_cache()

        # 获取刷新后的缓存信息
        cache_info = MappingService.get_cache_info()

        return BaseResponse(
            success=True,
            message="缓存刷新成功",
            data=cache_info
        )
    except Exception as e:
        logger.error(f"刷新缓存时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"刷新缓存失败: {str(e)}")


@router.post("/cache/invalidate", response_model=BaseResponse)
async def invalidate_cache():
    """使映射规则缓存失效"""
    try:
        MappingService.invalidate_cache()
        return BaseResponse(
            success=True,
            message="缓存已失效"
        )
    except Exception as e:
        logger.error(f"使缓存失效时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"使缓存失效失败: {str(e)}")
