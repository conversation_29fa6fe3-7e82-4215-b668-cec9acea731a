"""
演员管理 API 路由
提供演员的 CRUD 操作接口
"""
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.services.management_service import ActorService
from app.schemas.schemas import (
    ActorCreate, ActorUpdate, ActorResponse, ActorListResponse, ActorListRequest, BaseResponse,
    UnifiedDeleteRequest, MergeRequest, MergeResult, MergeCheckResponse,
    RenameConflictCheckRequest, RenameConflictCheckResponse
)
from typing import List
import logging

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(
    prefix="/actors",
    tags=["演员管理"],
    responses={404: {"description": "演员不存在"}}
)


@router.post("/add", response_model=BaseResponse)
async def create_actor(actor_data: ActorCreate, db: Session = Depends(get_db)):
    """
    创建新演员
    
    Args:
        actor_data: 演员创建数据
        db: 数据库会话
        
    Returns:
        创建结果
    """
    try:
        actor_service = ActorService(db)
        actor = actor_service.create_actor(actor_data)
        
        if not actor:
            raise HTTPException(status_code=400, detail="演员名称已存在")
        
        return BaseResponse(
            success=True,
            message="演员创建成功",
            data=ActorResponse.model_validate(actor)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建演员时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"创建演员失败: {str(e)}")


@router.post("/list", response_model=ActorListResponse)
async def get_actors(request: ActorListRequest, db: Session = Depends(get_db)):
    """
    获取演员列表

    Args:
        request: 演员列表请求参数
        db: 数据库会话

    Returns:
        演员列表
    """
    try:
        # 参数验证
        if request.limit < 1 or request.limit > 1000:
            raise HTTPException(status_code=400, detail="limit 必须在 1-1000 之间")
        if request.offset < 0:
            raise HTTPException(status_code=400, detail="offset 不能为负数")

        actor_service = ActorService(db)
        actors_with_counts, total_count = actor_service.get_actors_with_movie_counts(
            request.limit,
            request.offset,
            request.search
        )

        # 构建响应数据
        actor_responses = []
        for actor_info in actors_with_counts:
            actor_response = ActorResponse.model_validate(actor_info["actor"])
            actor_response.movie_count = actor_info["movie_count"]
            actor_responses.append(actor_response)

        return ActorListResponse(
            success=True,
            message=f"获取到 {len(actors_with_counts)} 个演员",
            data=actor_responses,
            total_count=total_count,
            limit=request.limit,
            offset=request.offset
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取演员列表时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取演员列表失败: {str(e)}")


@router.get("/info/{actor_id}", response_model=BaseResponse)
async def get_actor(actor_id: int, db: Session = Depends(get_db)):
    """
    获取演员详情
    
    Args:
        actor_id: 演员ID
        db: 数据库会话
        
    Returns:
        演员详情
    """
    try:
        actor_service = ActorService(db)
        actor = actor_service.get_actor_by_id(actor_id)
        
        if not actor:
            raise HTTPException(status_code=404, detail="演员不存在")
        
        actor_response = ActorResponse.model_validate(actor_info["actor"])
        actor_response.movie_count = actor_info["movie_count"]
        
        return BaseResponse(
            success=True,
            message="获取演员详情成功",
            data=actor_response
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取演员详情时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取演员详情失败: {str(e)}")


@router.put("/edit/{actor_id}", response_model=BaseResponse)
async def update_actor(actor_id: int, update_data: ActorUpdate, db: Session = Depends(get_db)):
    """
    更新演员信息
    
    Args:
        actor_id: 演员ID
        update_data: 更新数据
        db: 数据库会话
        
    Returns:
        更新结果
    """
    try:
        actor_service = ActorService(db)
        actor = actor_service.update_actor(actor_id, update_data)

        if not actor:
            raise HTTPException(status_code=404, detail="演员不存在")
        
        return BaseResponse(
            success=True,
            message="演员信息更新成功",
            data=ActorResponse.model_validate(actor)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新演员信息时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"更新演员信息失败: {str(e)}")


@router.delete("", response_model=BaseResponse)
async def delete_actors(request: UnifiedDeleteRequest, db: Session = Depends(get_db)):
    """
    统一删除演员（支持单项和批量删除）

    Args:
        request: 统一删除请求，支持单个ID或ID列表
        db: 数据库会话

    Returns:
        删除结果
    """
    try:
        actor_service = ActorService(db)
        result = actor_service.unified_delete_actors(request)

        if isinstance(result, tuple):
            # 单项删除结果
            success, message = result
            if not success:
                if "不存在" in message:
                    raise HTTPException(status_code=404, detail=message)
                else:
                    raise HTTPException(status_code=400, detail=message)

            return BaseResponse(
                success=True,
                message=message
            )
        else:
            # 批量删除结果
            batch_result = result
            if batch_result.failed_count == 0:
                message = f"成功删除 {batch_result.success_count} 个演员"
            elif batch_result.success_count == 0:
                message = f"删除失败，{batch_result.failed_count} 个演员无法删除"
            else:
                message = f"成功删除 {batch_result.success_count} 个演员，{batch_result.failed_count} 个失败"

            return BaseResponse(
                success=True,
                message=message,
                data=batch_result
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除演员时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"删除演员失败: {str(e)}")


@router.post("/check-rename-conflict", response_model=RenameConflictCheckResponse)
async def check_rename_conflict(request: RenameConflictCheckRequest, db: Session = Depends(get_db)):
    """
    检查演员重命名是否会产生冲突

    Args:
        request: 重命名冲突检查请求
        db: 数据库会话

    Returns:
        冲突检查结果
    """
    try:
        actor_service = ActorService(db)
        result = actor_service.check_rename_conflict(request.item_id, request.new_name)

        return RenameConflictCheckResponse(
            has_conflict=result["has_conflict"],
            can_auto_merge=result["can_auto_merge"],
            conflict_info=result.get("conflict_info"),
            message=result["message"]
        )

    except Exception as e:
        logger.error(f"检查演员重命名冲突时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"检查失败: {str(e)}")


@router.post("/check-merge-conflict", response_model=MergeCheckResponse)
async def check_merge_conflict(request: dict, db: Session = Depends(get_db)):
    """
    检查演员合并是否会产生冲突

    Args:
        request: 包含 target_name 和 source_ids 的请求体
        db: 数据库会话

    Returns:
        合并冲突检查结果
    """
    try:
        target_name = request.get("target_name")
        source_ids = request.get("source_ids")

        if not target_name:
            raise HTTPException(status_code=400, detail="target_name 是必需的")
        if not source_ids or not isinstance(source_ids, list):
            raise HTTPException(status_code=400, detail="source_ids 必须是非空列表")

        actor_service = ActorService(db)
        result = actor_service.check_merge_conflict(target_name, source_ids)

        return MergeCheckResponse(
            can_merge=result["can_merge"],
            conflict_info=result.get("conflict_info"),
            message=result["message"]
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"检查演员合并冲突时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"检查失败: {str(e)}")


@router.post("/merge", response_model=MergeResult)
async def merge_actors(request: MergeRequest, db: Session = Depends(get_db)):
    """
    合并多个演员

    Args:
        request: 合并请求
        db: 数据库会话

    Returns:
        合并结果
    """
    try:
        actor_service = ActorService(db)
        result = actor_service.merge_actors(
            source_ids=request.source_ids,
            target_name=request.target_name,
            target_description=request.target_description
        )

        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["message"])

        return MergeResult(
            success=result["success"],
            target_item_id=result["target_item_id"],
            target_item_name=result["target_item_name"],
            merged_count=result["merged_count"],
            total_movies_affected=result["total_movies_affected"],
            message=result["message"]
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"合并演员时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"合并失败: {str(e)}")
