"""
标签、分类和系列管理服务
提供完整的 CRUD 操作和关联关系检查
"""
from typing import List, Optional, Tuple, Dict, Any, Union
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from sqlalchemy import func, or_
from datetime import datetime
from app.models.models import Tag, Genre, Series, Movie, Actor, movie_tags, movie_genres, movie_actors
from app.schemas.schemas import (
    TagCreate, TagUpdate, GenreCreate, GenreUpdate,
    SeriesCreate, SeriesUpdate, ActorCreate, ActorUpdate,
    BatchDeleteRequest, BatchDeleteResult, BatchDeleteFailedItem,
    UnifiedDeleteRequest, MergeRequest, MergeResult, MergeCheckResponse,
    RenameConflictCheckRequest, RenameConflictCheckResponse, MergeConflictInfo
)
import logging
import time
import locale
from functools import cmp_to_key

logger = logging.getLogger(__name__)

# 设置中文排序支持
try:
    # 尝试设置中文locale
    locale.setlocale(locale.LC_COLLATE, 'zh_CN.UTF-8')
except locale.Error:
    try:
        # Windows系统尝试
        locale.setlocale(locale.LC_COLLATE, 'Chinese_China.936')
    except locale.Error:
        try:
            # 备用方案
            locale.setlocale(locale.LC_COLLATE, 'C.UTF-8')
        except locale.Error:
            # 最后备用方案
            pass

def chinese_sort_key(text):
    """
    生成中文排序键
    支持中文拼音排序和英文字母排序
    """
    if not text:
        return ""

    try:
        # 使用locale进行排序
        return locale.strxfrm(str(text))
    except:
        # 如果locale失败，使用简单的字符串排序
        return str(text).lower()


class NameConflictResolver:
    """名称冲突解决器 - 用于处理重命名时的名称冲突"""

    def __init__(self, db: Session):
        self.db = db

    def resolve_name_conflict(self, model_class, new_name: str, exclude_id: int = None) -> str:
        """
        解决名称冲突，如果名称已存在则自动添加数字后缀

        Args:
            model_class: 模型类 (Tag, Genre, Series, Actor)
            new_name: 新名称
            exclude_id: 要排除的ID（用于更新时排除自己）

        Returns:
            解决冲突后的名称
        """
        original_name = new_name.strip()
        counter = 1
        resolved_name = original_name

        while True:
            # 构建查询条件
            query = self.db.query(model_class).filter(model_class.name == resolved_name)
            if exclude_id is not None:
                query = query.filter(model_class.id != exclude_id)

            # 检查是否存在冲突
            existing = query.first()
            if not existing:
                break

            # 生成新的名称
            resolved_name = f"{original_name}{counter}"
            counter += 1

            # 防止无限循环，最多尝试1000次
            if counter > 1000:
                resolved_name = f"{original_name}_{int(time.time())}"
                break

        return resolved_name


class MovieUpdateMarkService:
    """影片更新标记服务 - 用于在管理操作后标记相关影片为已更新"""

    def __init__(self, db: Session):
        self.db = db

    def mark_movies_updated_by_tag(self, tag_id: int) -> int:
        """标记与指定标签关联的所有影片为已更新"""
        try:
            current_time = datetime.now()
            # 先获取所有关联的影片ID
            movie_ids = self.db.query(Movie.id).join(Movie.tags).filter(Tag.id == tag_id).all()
            movie_ids = [movie_id[0] for movie_id in movie_ids]

            if not movie_ids:
                return 0

            # 批量更新影片
            result = self.db.query(Movie).filter(Movie.id.in_(movie_ids)).update(
                {Movie.is_updated: True, Movie.updated_at: current_time},
                synchronize_session=False
            )
            self.db.commit()
            logger.info(f"标记了 {result} 部与标签 ID {tag_id} 关联的影片为已更新")
            return result
        except Exception as e:
            self.db.rollback()
            logger.error(f"标记标签关联影片更新时发生错误: {e}")
            return 0

    def mark_movies_updated_by_genre(self, genre_id: int) -> int:
        """标记与指定分类关联的所有影片为已更新"""
        try:
            current_time = datetime.now()
            # 先获取所有关联的影片ID
            movie_ids = self.db.query(Movie.id).join(Movie.genres).filter(Genre.id == genre_id).all()
            movie_ids = [movie_id[0] for movie_id in movie_ids]

            if not movie_ids:
                return 0

            # 批量更新影片
            result = self.db.query(Movie).filter(Movie.id.in_(movie_ids)).update(
                {Movie.is_updated: True, Movie.updated_at: current_time},
                synchronize_session=False
            )
            self.db.commit()
            logger.info(f"标记了 {result} 部与分类 ID {genre_id} 关联的影片为已更新")
            return result
        except Exception as e:
            self.db.rollback()
            logger.error(f"标记分类关联影片更新时发生错误: {e}")
            return 0

    def mark_movies_updated_by_series(self, series_id: int) -> int:
        """标记与指定系列关联的所有影片为已更新"""
        try:
            current_time = datetime.now()
            result = self.db.query(Movie).filter(Movie.series_id == series_id).update(
                {Movie.is_updated: True, Movie.updated_at: current_time},
                synchronize_session=False
            )
            self.db.commit()
            logger.info(f"标记了 {result} 部与系列 ID {series_id} 关联的影片为已更新")
            return result
        except Exception as e:
            self.db.rollback()
            logger.error(f"标记系列关联影片更新时发生错误: {e}")
            return 0

    def mark_movies_updated_by_actor(self, actor_id: int) -> int:
        """标记与指定演员关联的所有影片为已更新"""
        try:
            current_time = datetime.now()
            # 先获取所有关联的影片ID
            movie_ids = self.db.query(Movie.id).join(Movie.actors).filter(Actor.id == actor_id).all()
            movie_ids = [movie_id[0] for movie_id in movie_ids]

            if not movie_ids:
                return 0

            # 批量更新影片
            result = self.db.query(Movie).filter(Movie.id.in_(movie_ids)).update(
                {Movie.is_updated: True, Movie.updated_at: current_time},
                synchronize_session=False
            )
            self.db.commit()
            logger.info(f"标记了 {result} 部与演员 ID {actor_id} 关联的影片为已更新")
            return result
        except Exception as e:
            self.db.rollback()
            logger.error(f"标记演员关联影片更新时发生错误: {e}")
            return 0


class TagService:
    """标签管理服务"""

    def __init__(self, db: Session):
        self.db = db
        self.movie_update_service = MovieUpdateMarkService(db)
        self.name_resolver = NameConflictResolver(db)
    
    def create_tag(self, tag_data: TagCreate) -> Optional[Tag]:
        """
        创建新标签
        
        Args:
            tag_data: 标签创建数据
            
        Returns:
            创建的标签对象，如果失败则返回 None
        """
        try:
            # 检查名称是否已存在
            existing_tag = self.db.query(Tag).filter(Tag.name == tag_data.name).first()
            if existing_tag:
                logger.warning(f"标签名称已存在: {tag_data.name}")
                return None
            
            tag = Tag(
                name=tag_data.name,
                description=tag_data.description
            )
            
            self.db.add(tag)
            self.db.commit()
            self.db.refresh(tag)
            
            logger.info(f"成功创建标签: {tag.name} (ID: {tag.id})")
            return tag
            
        except IntegrityError:
            self.db.rollback()
            logger.error(f"标签名称重复: {tag_data.name}")
            return None
        except Exception as e:
            self.db.rollback()
            logger.error(f"创建标签时发生错误: {e}")
            return None
    


    def get_tags_with_movie_counts(self, limit: int = 50, offset: int = 0, search: Optional[str] = None) -> Tuple[List[Dict[str, Any]], int]:
        """
        获取标签列表及其电影数量（批量查询优化版本）

        Args:
            limit: 每页数量
            offset: 偏移量
            search: 搜索关键词

        Returns:
            (标签列表及电影数量, 总数量)
        """
        try:
            from sqlalchemy import func

            # 构建基础查询
            base_query = self.db.query(Tag)

            # 搜索过滤
            if search:
                search_pattern = f"%{search}%"
                base_query = base_query.filter(
                    or_(
                        Tag.name.ilike(search_pattern),
                        Tag.description.ilike(search_pattern)
                    )
                )

            # 获取总数量
            total_count = base_query.count()

            # 先获取所有符合条件的标签（不分页）
            all_tags = base_query.all()

            # 在应用层进行中文拼音排序
            def sort_tags(tag):
                # 主排序键：名称（支持中文拼音排序）
                name_key = chinese_sort_key(tag.name)
                # 次排序键：创建时间（倒序，所以取负值）
                time_key = -tag.created_at.timestamp() if tag.created_at else 0
                return (name_key, time_key)

            sorted_tags = sorted(all_tags, key=sort_tags)

            # 应用分页
            tags = sorted_tags[offset:offset + limit]

            if not tags:
                return [], total_count

            # 批量查询电影数量
            tag_ids = [tag.id for tag in tags]
            movie_counts = self.db.query(
                movie_tags.c.tag_id,
                func.count(movie_tags.c.movie_id).label('movie_count')
            ).filter(
                movie_tags.c.tag_id.in_(tag_ids)
            ).group_by(movie_tags.c.tag_id).all()

            # 构建电影数量映射
            count_map = {tag_id: count for tag_id, count in movie_counts}

            # 构建结果列表
            result = []
            for tag in tags:
                result.append({
                    'tag': tag,
                    'movie_count': count_map.get(tag.id, 0)
                })

            return result, total_count

        except Exception as e:
            logger.error(f"获取标签列表及电影数量时发生错误: {e}")
            return [], 0
    
    def get_tag_by_id(self, tag_id: int) -> Optional[Tag]:
        """根据 ID 获取标签"""
        try:
            return self.db.query(Tag).filter(Tag.id == tag_id).first()
        except Exception as e:
            logger.error(f"获取标签时发生错误: {e}")
            return None
    
    def update_tag(self, tag_id: int, tag_data: TagUpdate) -> Optional[Tag]:
        """
        更新标签

        Args:
            tag_id: 标签 ID
            tag_data: 更新数据

        Returns:
            更新后的标签对象，如果失败则返回 None
        """
        try:
            tag = self.get_tag_by_id(tag_id)
            if not tag:
                return None

            # 解决名称冲突
            if tag_data.name and tag_data.name != tag.name:
                resolved_name = self.name_resolver.resolve_name_conflict(Tag, tag_data.name, tag_id)
                if resolved_name != tag_data.name:
                    logger.info(f"标签名称冲突已解决: '{tag_data.name}' -> '{resolved_name}'")
                tag_data.name = resolved_name

            # 更新字段
            if tag_data.name is not None:
                tag.name = tag_data.name
            if tag_data.description is not None:
                tag.description = tag_data.description

            self.db.commit()
            self.db.refresh(tag)

            # 标记相关影片为已更新
            updated_movies_count = self.movie_update_service.mark_movies_updated_by_tag(tag_id)

            logger.info(f"成功更新标签: {tag.name} (ID: {tag.id})，标记了 {updated_movies_count} 部相关影片为已更新")
            return tag
            
        except IntegrityError:
            self.db.rollback()
            logger.error(f"更新标签时名称重复: {tag_data.name}")
            return None
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新标签时发生错误: {e}")
            return None
    
    def delete_tag(self, tag_id: int, force: bool = False) -> Tuple[bool, str]:
        """
        删除标签

        Args:
            tag_id: 标签 ID
            force: 是否强制删除

        Returns:
            (是否成功, 消息)
        """
        try:
            tag = self.get_tag_by_id(tag_id)
            if not tag:
                return False, "标签不存在"

            # 检查关联的电影数量
            movie_count = self.db.query(Movie).join(Movie.tags).filter(Tag.id == tag_id).count()

            if movie_count > 0 and not force:
                return False, f"标签被 {movie_count} 部电影使用，无法删除。使用 force=true 强制删除。"

            # 在删除前标记相关影片为已更新
            updated_movies_count = 0
            if movie_count > 0:
                updated_movies_count = self.movie_update_service.mark_movies_updated_by_tag(tag_id)

            self.db.delete(tag)
            self.db.commit()

            message = f"成功删除标签: {tag.name}"
            if movie_count > 0:
                message += f"（强制删除，影响 {movie_count} 部电影，已标记 {updated_movies_count} 部影片为已更新）"

            logger.info(message)
            return True, message
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"删除标签时发生错误: {e}")
            return False, f"删除失败: {str(e)}"
    
    def get_tag_with_movie_count(self, tag_id: int) -> Optional[Dict[str, Any]]:
        """获取标签及其关联的电影数量（优化版本）"""
        try:
            tag = self.get_tag_by_id(tag_id)
            if not tag:
                return None

            # 使用关联表直接查询，避免JOIN
            movie_count = self.db.query(movie_tags.c.movie_id).filter(
                movie_tags.c.tag_id == tag_id
            ).count()

            return {
                "tag": tag,
                "movie_count": movie_count
            }

        except Exception as e:
            logger.error(f"获取标签详情时发生错误: {e}")
            return None
    


    def batch_delete_tags(self, request: BatchDeleteRequest) -> BatchDeleteResult:
        """
        批量删除标签

        Args:
            request: 批量删除请求

        Returns:
            批量删除结果
        """
        success_ids = []
        failed_items = []

        for tag_id in request.ids:
            try:
                success, message = self.delete_tag(tag_id, force=request.force)
                if success:
                    success_ids.append(tag_id)
                else:
                    failed_items.append(BatchDeleteFailedItem(id=tag_id, error=message))
            except Exception as e:
                failed_items.append(BatchDeleteFailedItem(id=tag_id, error=f"删除失败: {str(e)}"))

        return BatchDeleteResult(
            total_count=len(request.ids),
            success_count=len(success_ids),
            failed_count=len(failed_items),
            success_ids=success_ids,
            failed_items=failed_items
        )

    def unified_delete_tags(self, request: UnifiedDeleteRequest) -> Union[Tuple[bool, str], BatchDeleteResult]:
        """
        统一删除标签（支持单项和批量）

        Args:
            request: 统一删除请求

        Returns:
            单项删除返回 (bool, str)，批量删除返回 BatchDeleteResult
        """
        if len(request.ids) == 1:
            # 单项删除
            return self.delete_tag(request.ids[0], force=request.force)
        else:
            # 批量删除
            batch_request = BatchDeleteRequest(ids=request.ids, force=request.force)
            return self.batch_delete_tags(batch_request)

    def check_rename_conflict(self, tag_id: int, new_name: str) -> Dict[str, Any]:
        """
        检查重命名是否会产生冲突

        Args:
            tag_id: 要重命名的标签ID
            new_name: 新名称

        Returns:
            冲突检查结果
        """
        try:
            # 检查标签是否存在
            tag = self.get_tag_by_id(tag_id)
            if not tag:
                return {
                    "has_conflict": False,
                    "can_auto_merge": False,
                    "message": "标签不存在"
                }

            # 如果新名称与当前名称相同，无冲突
            if tag.name == new_name:
                return {
                    "has_conflict": False,
                    "can_auto_merge": False,
                    "message": "名称未发生变化"
                }

            # 检查是否存在同名标签
            existing_tag = self.db.query(Tag).filter(
                Tag.name == new_name,
                Tag.id != tag_id
            ).first()

            if existing_tag:
                # 获取现有标签的影片数量
                movie_count = self.db.query(Movie).join(Movie.tags).filter(Tag.id == existing_tag.id).count()

                return {
                    "has_conflict": True,
                    "can_auto_merge": True,
                    "conflict_info": {
                        "existing_item_id": existing_tag.id,
                        "existing_item_name": existing_tag.name,
                        "movie_count": movie_count
                    },
                    "message": f"标签名称 '{new_name}' 已存在，可以选择合并"
                }
            else:
                return {
                    "has_conflict": False,
                    "can_auto_merge": False,
                    "message": "可以安全重命名"
                }

        except Exception as e:
            logger.error(f"检查标签重命名冲突时发生错误: {e}")
            return {
                "has_conflict": False,
                "can_auto_merge": False,
                "message": f"检查失败: {str(e)}"
            }

    def check_merge_conflict(self, target_name: str, source_ids: List[int]) -> Dict[str, Any]:
        """
        检查合并是否会产生冲突

        Args:
            target_name: 目标名称
            source_ids: 源标签ID列表

        Returns:
            冲突检查结果
        """
        try:
            # 检查是否存在同名标签
            existing_tag = self.db.query(Tag).filter(Tag.name == target_name).first()

            if existing_tag:
                # 如果现有标签在源列表中，可以直接合并
                if existing_tag.id in source_ids:
                    return {
                        "can_merge": True,
                        "message": "目标名称对应的标签在合并列表中，可以直接合并"
                    }
                else:
                    # 获取现有标签的影片数量（优化查询）
                    movie_count = self.db.query(movie_tags.c.movie_id).filter(movie_tags.c.tag_id == existing_tag.id).count()

                    return {
                        "can_merge": False,
                        "conflict_info": {
                            "existing_item_id": existing_tag.id,
                            "existing_item_name": existing_tag.name,
                            "movie_count": movie_count
                        },
                        "message": f"标签名称 '{target_name}' 已存在，需要处理冲突"
                    }
            else:
                return {
                    "can_merge": True,
                    "message": "可以创建新标签进行合并"
                }

        except Exception as e:
            logger.error(f"检查标签合并冲突时发生错误: {e}")
            return {
                "can_merge": False,
                "message": f"检查失败: {str(e)}"
            }

    def merge_tags(self, source_ids: List[int], target_name: str, target_description: Optional[str] = None) -> Dict[str, Any]:
        """
        合并多个标签

        Args:
            source_ids: 要合并的源标签ID列表
            target_name: 目标标签名称
            target_description: 目标标签描述

        Returns:
            合并结果
        """
        try:
            # 开始事务
            # 验证源标签是否都存在
            source_tags = []
            for tag_id in source_ids:
                tag = self.get_tag_by_id(tag_id)
                if not tag:
                    return {
                        "success": False,
                        "message": f"标签 ID {tag_id} 不存在"
                    }
                source_tags.append(tag)

            # 检查是否存在目标名称的标签
            existing_target = self.db.query(Tag).filter(Tag.name == target_name).first()

            if existing_target and existing_target.id not in source_ids:
                return {
                    "success": False,
                    "message": f"目标名称 '{target_name}' 已存在且不在合并列表中"
                }

            # 收集所有关联的影片
            all_movie_ids = set()
            for tag in source_tags:
                movie_ids = self.db.query(Movie.id).join(Movie.tags).filter(Tag.id == tag.id).all()
                all_movie_ids.update([movie_id[0] for movie_id in movie_ids])

            # 确定目标标签
            if existing_target and existing_target.id in source_ids:
                # 使用现有标签作为目标
                target_tag = existing_target
                # 更新描述（如果提供）
                if target_description is not None:
                    target_tag.description = target_description
                # 从源列表中移除目标标签
                source_tags = [tag for tag in source_tags if tag.id != target_tag.id]
            else:
                # 创建新的目标标签
                target_tag = Tag(
                    name=target_name,
                    description=target_description
                )
                self.db.add(target_tag)
                self.db.flush()  # 获取ID但不提交

            # 将所有影片关联到目标标签
            for movie_id in all_movie_ids:
                movie = self.db.query(Movie).filter(Movie.id == movie_id).first()
                if movie and target_tag not in movie.tags:
                    movie.tags.append(target_tag)

            # 删除源标签（除了目标标签）
            deleted_count = 0
            for tag in source_tags:
                # 先移除所有关联关系
                tag.movies.clear()
                self.db.delete(tag)
                deleted_count += 1

            # 提交事务
            self.db.commit()
            self.db.refresh(target_tag)

            logger.info(f"成功合并 {len(source_ids)} 个标签到 '{target_name}' (ID: {target_tag.id})")

            return {
                "success": True,
                "target_item_id": target_tag.id,
                "target_item_name": target_tag.name,
                "merged_count": len(source_ids),
                "total_movies_affected": len(all_movie_ids),
                "message": f"成功合并 {len(source_ids)} 个标签，影响 {len(all_movie_ids)} 部影片"
            }

        except Exception as e:
            self.db.rollback()
            logger.error(f"合并标签时发生错误: {e}")
            return {
                "success": False,
                "message": f"合并失败: {str(e)}"
            }


class GenreService:
    """分类管理服务"""

    def __init__(self, db: Session):
        self.db = db
        self.movie_update_service = MovieUpdateMarkService(db)
        self.name_resolver = NameConflictResolver(db)
    
    def create_genre(self, genre_data: GenreCreate) -> Optional[Genre]:
        """创建新分类"""
        try:
            # 检查名称是否已存在
            existing_genre = self.db.query(Genre).filter(Genre.name == genre_data.name).first()
            if existing_genre:
                logger.warning(f"分类名称已存在: {genre_data.name}")
                return None
            
            genre = Genre(
                name=genre_data.name,
                description=genre_data.description
            )
            
            self.db.add(genre)
            self.db.commit()
            self.db.refresh(genre)
            
            logger.info(f"成功创建分类: {genre.name} (ID: {genre.id})")
            return genre
            
        except IntegrityError:
            self.db.rollback()
            logger.error(f"分类名称重复: {genre_data.name}")
            return None
        except Exception as e:
            self.db.rollback()
            logger.error(f"创建分类时发生错误: {e}")
            return None
    


    def get_genres_with_movie_counts(self, limit: int = 50, offset: int = 0, search: Optional[str] = None) -> Tuple[List[Dict[str, Any]], int]:
        """
        获取分类列表及其电影数量（批量查询优化版本）

        Args:
            limit: 每页数量
            offset: 偏移量
            search: 搜索关键词

        Returns:
            (分类列表及电影数量, 总数量)
        """
        try:
            # 构建基础查询
            base_query = self.db.query(Genre)

            # 搜索过滤
            if search:
                search_pattern = f"%{search}%"
                base_query = base_query.filter(
                    or_(
                        Genre.name.ilike(search_pattern),
                        Genre.description.ilike(search_pattern)
                    )
                )

            # 获取总数量
            total_count = base_query.count()

            # 先获取所有符合条件的分类（不分页）
            all_genres = base_query.all()

            # 在应用层进行中文拼音排序
            def sort_genres(genre):
                # 主排序键：名称（支持中文拼音排序）
                name_key = chinese_sort_key(genre.name)
                # 次排序键：创建时间（倒序，所以取负值）
                time_key = -genre.created_at.timestamp() if genre.created_at else 0
                return (name_key, time_key)

            sorted_genres = sorted(all_genres, key=sort_genres)

            # 应用分页
            genres = sorted_genres[offset:offset + limit]

            if not genres:
                return [], total_count

            # 批量查询电影数量
            genre_ids = [genre.id for genre in genres]
            movie_counts = self.db.query(
                movie_genres.c.genre_id,
                func.count(movie_genres.c.movie_id).label('movie_count')
            ).filter(
                movie_genres.c.genre_id.in_(genre_ids)
            ).group_by(movie_genres.c.genre_id).all()

            # 构建电影数量映射
            count_map = {genre_id: count for genre_id, count in movie_counts}

            # 构建结果列表
            result = []
            for genre in genres:
                result.append({
                    'genre': genre,
                    'movie_count': count_map.get(genre.id, 0)
                })

            return result, total_count

        except Exception as e:
            logger.error(f"获取分类列表及电影数量时发生错误: {e}")
            return [], 0
    
    def get_genre_by_id(self, genre_id: int) -> Optional[Genre]:
        """根据 ID 获取分类"""
        try:
            return self.db.query(Genre).filter(Genre.id == genre_id).first()
        except Exception as e:
            logger.error(f"获取分类时发生错误: {e}")
            return None
    
    def update_genre(self, genre_id: int, genre_data: GenreUpdate) -> Optional[Genre]:
        """更新分类"""
        try:
            genre = self.get_genre_by_id(genre_id)
            if not genre:
                return None

            # 解决名称冲突
            if genre_data.name and genre_data.name != genre.name:
                resolved_name = self.name_resolver.resolve_name_conflict(Genre, genre_data.name, genre_id)
                if resolved_name != genre_data.name:
                    logger.info(f"分类名称冲突已解决: '{genre_data.name}' -> '{resolved_name}'")
                genre_data.name = resolved_name

            # 更新字段
            if genre_data.name is not None:
                genre.name = genre_data.name
            if genre_data.description is not None:
                genre.description = genre_data.description

            self.db.commit()
            self.db.refresh(genre)

            # 标记相关影片为已更新
            updated_movies_count = self.movie_update_service.mark_movies_updated_by_genre(genre_id)

            logger.info(f"成功更新分类: {genre.name} (ID: {genre.id})，标记了 {updated_movies_count} 部相关影片为已更新")
            return genre
            
        except IntegrityError:
            self.db.rollback()
            logger.error(f"更新分类时名称重复: {genre_data.name}")
            return None
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新分类时发生错误: {e}")
            return None
    
    def delete_genre(self, genre_id: int, force: bool = False) -> Tuple[bool, str]:
        """删除分类"""
        try:
            genre = self.get_genre_by_id(genre_id)
            if not genre:
                return False, "分类不存在"

            # 检查关联的电影数量
            movie_count = self.db.query(Movie).join(Movie.genres).filter(Genre.id == genre_id).count()

            if movie_count > 0 and not force:
                return False, f"分类被 {movie_count} 部电影使用，无法删除。使用 force=true 强制删除。"

            # 在删除前标记相关影片为已更新
            updated_movies_count = 0
            if movie_count > 0:
                updated_movies_count = self.movie_update_service.mark_movies_updated_by_genre(genre_id)

            self.db.delete(genre)
            self.db.commit()

            message = f"成功删除分类: {genre.name}"
            if movie_count > 0:
                message += f"（强制删除，影响 {movie_count} 部电影，已标记 {updated_movies_count} 部影片为已更新）"

            logger.info(message)
            return True, message
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"删除分类时发生错误: {e}")
            return False, f"删除失败: {str(e)}"
    
    def get_genre_with_movie_count(self, genre_id: int) -> Optional[Dict[str, Any]]:
        """获取分类及其关联的电影数量（优化版本）"""
        try:
            genre = self.get_genre_by_id(genre_id)
            if not genre:
                return None

            # 使用关联表直接查询，避免JOIN
            movie_count = self.db.query(movie_genres.c.movie_id).filter(
                movie_genres.c.genre_id == genre_id
            ).count()

            return {
                "genre": genre,
                "movie_count": movie_count
            }

        except Exception as e:
            logger.error(f"获取分类详情时发生错误: {e}")
            return None
    


    def batch_delete_genres(self, request: BatchDeleteRequest) -> BatchDeleteResult:
        """
        批量删除分类

        Args:
            request: 批量删除请求

        Returns:
            批量删除结果
        """
        success_ids = []
        failed_items = []

        for genre_id in request.ids:
            try:
                success, message = self.delete_genre(genre_id, force=request.force)
                if success:
                    success_ids.append(genre_id)
                else:
                    failed_items.append(BatchDeleteFailedItem(id=genre_id, error=message))
            except Exception as e:
                failed_items.append(BatchDeleteFailedItem(id=genre_id, error=f"删除失败: {str(e)}"))

        return BatchDeleteResult(
            total_count=len(request.ids),
            success_count=len(success_ids),
            failed_count=len(failed_items),
            success_ids=success_ids,
            failed_items=failed_items
        )

    def unified_delete_genres(self, request: UnifiedDeleteRequest) -> Union[Tuple[bool, str], BatchDeleteResult]:
        """
        统一删除分类（支持单项和批量）

        Args:
            request: 统一删除请求

        Returns:
            单项删除返回 (bool, str)，批量删除返回 BatchDeleteResult
        """
        if len(request.ids) == 1:
            # 单项删除
            return self.delete_genre(request.ids[0], force=request.force)
        else:
            # 批量删除
            batch_request = BatchDeleteRequest(ids=request.ids, force=request.force)
            return self.batch_delete_genres(batch_request)

    def check_rename_conflict(self, genre_id: int, new_name: str) -> Dict[str, Any]:
        """
        检查重命名是否会产生冲突

        Args:
            genre_id: 要重命名的分类ID
            new_name: 新名称

        Returns:
            冲突检查结果
        """
        try:
            # 检查分类是否存在
            genre = self.get_genre_by_id(genre_id)
            if not genre:
                return {
                    "has_conflict": False,
                    "can_auto_merge": False,
                    "message": "分类不存在"
                }

            # 如果新名称与当前名称相同，无冲突
            if genre.name == new_name:
                return {
                    "has_conflict": False,
                    "can_auto_merge": False,
                    "message": "名称未发生变化"
                }

            # 检查是否存在同名分类
            existing_genre = self.db.query(Genre).filter(
                Genre.name == new_name,
                Genre.id != genre_id
            ).first()

            if existing_genre:
                # 获取现有分类的影片数量（优化查询）
                movie_count = self.db.query(movie_genres.c.movie_id).filter(movie_genres.c.genre_id == existing_genre.id).count()

                return {
                    "has_conflict": True,
                    "can_auto_merge": True,
                    "conflict_info": {
                        "existing_item_id": existing_genre.id,
                        "existing_item_name": existing_genre.name,
                        "movie_count": movie_count
                    },
                    "message": f"分类名称 '{new_name}' 已存在，可以选择合并"
                }
            else:
                return {
                    "has_conflict": False,
                    "can_auto_merge": False,
                    "message": "可以安全重命名"
                }

        except Exception as e:
            logger.error(f"检查分类重命名冲突时发生错误: {e}")
            return {
                "has_conflict": False,
                "can_auto_merge": False,
                "message": f"检查失败: {str(e)}"
            }

    def check_merge_conflict(self, target_name: str, source_ids: List[int]) -> Dict[str, Any]:
        """
        检查合并是否会产生冲突

        Args:
            target_name: 目标名称
            source_ids: 源分类ID列表

        Returns:
            冲突检查结果
        """
        try:
            # 检查是否存在同名分类
            existing_genre = self.db.query(Genre).filter(Genre.name == target_name).first()

            if existing_genre:
                # 如果现有分类在源列表中，可以直接合并
                if existing_genre.id in source_ids:
                    return {
                        "can_merge": True,
                        "message": "目标名称对应的分类在合并列表中，可以直接合并"
                    }
                else:
                    # 获取现有分类的影片数量（优化查询）
                    movie_count = self.db.query(movie_genres.c.movie_id).filter(movie_genres.c.genre_id == existing_genre.id).count()

                    return {
                        "can_merge": False,
                        "conflict_info": {
                            "existing_item_id": existing_genre.id,
                            "existing_item_name": existing_genre.name,
                            "movie_count": movie_count
                        },
                        "message": f"分类名称 '{target_name}' 已存在，需要处理冲突"
                    }
            else:
                return {
                    "can_merge": True,
                    "message": "可以创建新分类进行合并"
                }

        except Exception as e:
            logger.error(f"检查分类合并冲突时发生错误: {e}")
            return {
                "can_merge": False,
                "message": f"检查失败: {str(e)}"
            }

    def merge_genres(self, source_ids: List[int], target_name: str, target_description: Optional[str] = None) -> Dict[str, Any]:
        """
        合并多个分类

        Args:
            source_ids: 要合并的源分类ID列表
            target_name: 目标分类名称
            target_description: 目标分类描述

        Returns:
            合并结果
        """
        try:
            # 验证源分类是否都存在
            source_genres = []
            for genre_id in source_ids:
                genre = self.get_genre_by_id(genre_id)
                if not genre:
                    return {
                        "success": False,
                        "message": f"分类 ID {genre_id} 不存在"
                    }
                source_genres.append(genre)

            # 检查是否存在目标名称的分类
            existing_target = self.db.query(Genre).filter(Genre.name == target_name).first()

            if existing_target and existing_target.id not in source_ids:
                return {
                    "success": False,
                    "message": f"目标名称 '{target_name}' 已存在且不在合并列表中"
                }

            # 收集所有关联的影片
            all_movie_ids = set()
            for genre in source_genres:
                movie_ids = self.db.query(Movie.id).join(Movie.genres).filter(Genre.id == genre.id).all()
                all_movie_ids.update([movie_id[0] for movie_id in movie_ids])

            # 确定目标分类
            if existing_target and existing_target.id in source_ids:
                # 使用现有分类作为目标
                target_genre = existing_target
                # 更新描述（如果提供）
                if target_description is not None:
                    target_genre.description = target_description
                # 从源列表中移除目标分类
                source_genres = [genre for genre in source_genres if genre.id != target_genre.id]
            else:
                # 创建新的目标分类
                target_genre = Genre(
                    name=target_name,
                    description=target_description
                )
                self.db.add(target_genre)
                self.db.flush()  # 获取ID但不提交

            # 将所有影片关联到目标分类
            for movie_id in all_movie_ids:
                movie = self.db.query(Movie).filter(Movie.id == movie_id).first()
                if movie and target_genre not in movie.genres:
                    movie.genres.append(target_genre)

            # 删除源分类（除了目标分类）
            deleted_count = 0
            for genre in source_genres:
                # 先移除所有关联关系
                genre.movies.clear()
                self.db.delete(genre)
                deleted_count += 1

            # 提交事务
            self.db.commit()
            self.db.refresh(target_genre)

            logger.info(f"成功合并 {len(source_ids)} 个分类到 '{target_name}' (ID: {target_genre.id})")

            return {
                "success": True,
                "target_item_id": target_genre.id,
                "target_item_name": target_genre.name,
                "merged_count": len(source_ids),
                "total_movies_affected": len(all_movie_ids),
                "message": f"成功合并 {len(source_ids)} 个分类，影响 {len(all_movie_ids)} 部影片"
            }

        except Exception as e:
            self.db.rollback()
            logger.error(f"合并分类时发生错误: {e}")
            return {
                "success": False,
                "message": f"合并失败: {str(e)}"
            }


class SeriesService:
    """系列管理服务"""

    def __init__(self, db: Session):
        self.db = db
        self.movie_update_service = MovieUpdateMarkService(db)
        self.name_resolver = NameConflictResolver(db)

    def create_series(self, series_data: SeriesCreate) -> Optional[Series]:
        """创建新系列"""
        try:
            # 检查名称是否已存在
            existing_series = self.db.query(Series).filter(Series.name == series_data.name).first()
            if existing_series:
                logger.warning(f"系列名称已存在: {series_data.name}")
                return None

            series = Series(
                name=series_data.name,
                description=series_data.description
            )

            self.db.add(series)
            self.db.commit()
            self.db.refresh(series)

            logger.info(f"成功创建系列: {series.name} (ID: {series.id})")
            return series

        except IntegrityError:
            self.db.rollback()
            logger.error(f"系列名称重复: {series_data.name}")
            return None
        except Exception as e:
            self.db.rollback()
            logger.error(f"创建系列时发生错误: {e}")
            return None



    def get_series_list_with_movie_counts(self, limit: int = 50, offset: int = 0, search: Optional[str] = None) -> Tuple[List[Dict[str, Any]], int]:
        """
        获取系列列表及其电影数量（批量查询优化版本）

        Args:
            limit: 每页数量
            offset: 偏移量
            search: 搜索关键词

        Returns:
            (系列列表及电影数量, 总数量)
        """
        try:
            # 构建基础查询
            base_query = self.db.query(Series)

            # 搜索过滤
            if search:
                search_pattern = f"%{search}%"
                base_query = base_query.filter(
                    or_(
                        Series.name.ilike(search_pattern),
                        Series.description.ilike(search_pattern)
                    )
                )

            # 获取总数量
            total_count = base_query.count()

            # 先获取所有符合条件的系列（不分页）
            all_series = base_query.all()

            # 在应用层进行中文拼音排序
            def sort_series(series):
                # 主排序键：名称（支持中文拼音排序）
                name_key = chinese_sort_key(series.name)
                # 次排序键：创建时间（倒序，所以取负值）
                time_key = -series.created_at.timestamp() if series.created_at else 0
                return (name_key, time_key)

            sorted_series = sorted(all_series, key=sort_series)

            # 应用分页
            series_list = sorted_series[offset:offset + limit]

            if not series_list:
                return [], total_count

            # 批量查询电影数量
            series_ids = [series.id for series in series_list]
            movie_counts = self.db.query(
                Movie.series_id,
                func.count(Movie.id).label('movie_count')
            ).filter(
                Movie.series_id.in_(series_ids)
            ).group_by(Movie.series_id).all()

            # 构建电影数量映射
            count_map = {series_id: count for series_id, count in movie_counts}

            # 构建结果列表
            result = []
            for series in series_list:
                result.append({
                    'series': series,
                    'movie_count': count_map.get(series.id, 0)
                })

            return result, total_count

        except Exception as e:
            logger.error(f"获取系列列表及电影数量时发生错误: {e}")
            return [], 0

    def get_series_by_id(self, series_id: int) -> Optional[Series]:
        """根据 ID 获取系列"""
        try:
            return self.db.query(Series).filter(Series.id == series_id).first()
        except Exception as e:
            logger.error(f"获取系列时发生错误: {e}")
            return None

    def update_series(self, series_id: int, series_data: SeriesUpdate) -> Optional[Series]:
        """更新系列"""
        try:
            series = self.get_series_by_id(series_id)
            if not series:
                return None

            # 解决名称冲突
            if series_data.name and series_data.name != series.name:
                resolved_name = self.name_resolver.resolve_name_conflict(Series, series_data.name, series_id)
                if resolved_name != series_data.name:
                    logger.info(f"系列名称冲突已解决: '{series_data.name}' -> '{resolved_name}'")
                series_data.name = resolved_name

            # 更新字段
            if series_data.name is not None:
                series.name = series_data.name
            if series_data.description is not None:
                series.description = series_data.description

            self.db.commit()
            self.db.refresh(series)

            # 标记相关影片为已更新
            updated_movies_count = self.movie_update_service.mark_movies_updated_by_series(series_id)

            logger.info(f"成功更新系列: {series.name} (ID: {series.id})，标记了 {updated_movies_count} 部相关影片为已更新")
            return series

        except IntegrityError:
            self.db.rollback()
            logger.error(f"更新系列时名称重复: {series_data.name}")
            return None
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新系列时发生错误: {e}")
            return None

    def delete_series(self, series_id: int, force: bool = False) -> Tuple[bool, str]:
        """删除系列"""
        try:
            series = self.get_series_by_id(series_id)
            if not series:
                return False, "系列不存在"

            # 检查关联的电影数量
            movie_count = self.db.query(Movie).filter(Movie.series_id == series_id).count()

            if movie_count > 0 and not force:
                return False, f"系列被 {movie_count} 部电影使用，无法删除。使用 force=true 强制删除。"

            # 在删除前标记相关影片为已更新
            updated_movies_count = 0
            if movie_count > 0:
                updated_movies_count = self.movie_update_service.mark_movies_updated_by_series(series_id)

            self.db.delete(series)
            self.db.commit()

            message = f"成功删除系列: {series.name}"
            if movie_count > 0:
                message += f"（强制删除，影响 {movie_count} 部电影，已标记 {updated_movies_count} 部影片为已更新）"

            logger.info(message)
            return True, message

        except Exception as e:
            self.db.rollback()
            logger.error(f"删除系列时发生错误: {e}")
            return False, f"删除失败: {str(e)}"

    def get_series_with_movie_count(self, series_id: int) -> Optional[Dict[str, Any]]:
        """获取系列及其关联的电影数量（已优化）"""
        try:
            series = self.get_series_by_id(series_id)
            if not series:
                return None

            # 直接查询Movie表，这已经是最优的查询方式
            movie_count = self.db.query(Movie.id).filter(Movie.series_id == series_id).count()

            return {
                "series": series,
                "movie_count": movie_count
            }

        except Exception as e:
            logger.error(f"获取系列详情时发生错误: {e}")
            return None



    def batch_delete_series(self, request: BatchDeleteRequest) -> BatchDeleteResult:
        """
        批量删除系列

        Args:
            request: 批量删除请求

        Returns:
            批量删除结果
        """
        success_ids = []
        failed_items = []

        for series_id in request.ids:
            try:
                success, message = self.delete_series(series_id, force=request.force)
                if success:
                    success_ids.append(series_id)
                else:
                    failed_items.append(BatchDeleteFailedItem(id=series_id, error=message))
            except Exception as e:
                failed_items.append(BatchDeleteFailedItem(id=series_id, error=f"删除失败: {str(e)}"))

        return BatchDeleteResult(
            total_count=len(request.ids),
            success_count=len(success_ids),
            failed_count=len(failed_items),
            success_ids=success_ids,
            failed_items=failed_items
        )

    def unified_delete_series(self, request: UnifiedDeleteRequest) -> Union[Tuple[bool, str], BatchDeleteResult]:
        """
        统一删除系列（支持单项和批量）

        Args:
            request: 统一删除请求

        Returns:
            单项删除返回 (bool, str)，批量删除返回 BatchDeleteResult
        """
        if len(request.ids) == 1:
            # 单项删除
            return self.delete_series(request.ids[0], force=request.force)
        else:
            # 批量删除
            batch_request = BatchDeleteRequest(ids=request.ids, force=request.force)
            return self.batch_delete_series(batch_request)

    def check_rename_conflict(self, series_id: int, new_name: str) -> Dict[str, Any]:
        """
        检查重命名是否会产生冲突

        Args:
            series_id: 要重命名的系列ID
            new_name: 新名称

        Returns:
            冲突检查结果
        """
        try:
            # 检查系列是否存在
            series = self.get_series_by_id(series_id)
            if not series:
                return {
                    "has_conflict": False,
                    "can_auto_merge": False,
                    "message": "系列不存在"
                }

            # 如果新名称与当前名称相同，无冲突
            if series.name == new_name:
                return {
                    "has_conflict": False,
                    "can_auto_merge": False,
                    "message": "名称未发生变化"
                }

            # 检查是否存在同名系列
            existing_series = self.db.query(Series).filter(
                Series.name == new_name,
                Series.id != series_id
            ).first()

            if existing_series:
                # 获取现有系列的影片数量
                movie_count = self.db.query(Movie).filter(Movie.series_id == existing_series.id).count()

                return {
                    "has_conflict": True,
                    "can_auto_merge": True,
                    "conflict_info": {
                        "existing_item_id": existing_series.id,
                        "existing_item_name": existing_series.name,
                        "movie_count": movie_count
                    },
                    "message": f"系列名称 '{new_name}' 已存在，可以选择合并"
                }
            else:
                return {
                    "has_conflict": False,
                    "can_auto_merge": False,
                    "message": "可以安全重命名"
                }

        except Exception as e:
            logger.error(f"检查系列重命名冲突时发生错误: {e}")
            return {
                "has_conflict": False,
                "can_auto_merge": False,
                "message": f"检查失败: {str(e)}"
            }

    def check_merge_conflict(self, target_name: str, source_ids: List[int]) -> Dict[str, Any]:
        """
        检查合并是否会产生冲突

        Args:
            target_name: 目标名称
            source_ids: 源系列ID列表

        Returns:
            冲突检查结果
        """
        try:
            # 检查是否存在同名系列
            existing_series = self.db.query(Series).filter(Series.name == target_name).first()

            if existing_series:
                # 如果现有系列在源列表中，可以直接合并
                if existing_series.id in source_ids:
                    return {
                        "can_merge": True,
                        "message": "目标名称对应的系列在合并列表中，可以直接合并"
                    }
                else:
                    # 获取现有系列的影片数量
                    movie_count = self.db.query(Movie).filter(Movie.series_id == existing_series.id).count()

                    return {
                        "can_merge": False,
                        "conflict_info": {
                            "existing_item_id": existing_series.id,
                            "existing_item_name": existing_series.name,
                            "movie_count": movie_count
                        },
                        "message": f"系列名称 '{target_name}' 已存在，需要处理冲突"
                    }
            else:
                return {
                    "can_merge": True,
                    "message": "可以创建新系列进行合并"
                }

        except Exception as e:
            logger.error(f"检查系列合并冲突时发生错误: {e}")
            return {
                "can_merge": False,
                "message": f"检查失败: {str(e)}"
            }

    def merge_series(self, source_ids: List[int], target_name: str, target_description: Optional[str] = None) -> Dict[str, Any]:
        """
        合并多个系列

        Args:
            source_ids: 要合并的源系列ID列表
            target_name: 目标系列名称
            target_description: 目标系列描述

        Returns:
            合并结果
        """
        try:
            # 验证源系列是否都存在
            source_series_list = []
            for series_id in source_ids:
                series = self.get_series_by_id(series_id)
                if not series:
                    return {
                        "success": False,
                        "message": f"系列 ID {series_id} 不存在"
                    }
                source_series_list.append(series)

            # 检查是否存在目标名称的系列
            existing_target = self.db.query(Series).filter(Series.name == target_name).first()

            if existing_target and existing_target.id not in source_ids:
                return {
                    "success": False,
                    "message": f"目标名称 '{target_name}' 已存在且不在合并列表中"
                }

            # 收集所有关联的影片
            all_movie_ids = set()
            for series in source_series_list:
                movie_ids = self.db.query(Movie.id).filter(Movie.series_id == series.id).all()
                all_movie_ids.update([movie_id[0] for movie_id in movie_ids])

            # 确定目标系列
            if existing_target and existing_target.id in source_ids:
                # 使用现有系列作为目标
                target_series = existing_target
                # 更新描述（如果提供）
                if target_description is not None:
                    target_series.description = target_description
                # 从源列表中移除目标系列
                source_series_list = [series for series in source_series_list if series.id != target_series.id]
            else:
                # 创建新的目标系列
                target_series = Series(
                    name=target_name,
                    description=target_description
                )
                self.db.add(target_series)
                self.db.flush()  # 获取ID但不提交

            # 将所有影片关联到目标系列
            for movie_id in all_movie_ids:
                movie = self.db.query(Movie).filter(Movie.id == movie_id).first()
                if movie:
                    movie.series_id = target_series.id

            # 删除源系列（除了目标系列）
            deleted_count = 0
            for series in source_series_list:
                self.db.delete(series)
                deleted_count += 1

            # 提交事务
            self.db.commit()
            self.db.refresh(target_series)

            logger.info(f"成功合并 {len(source_ids)} 个系列到 '{target_name}' (ID: {target_series.id})")

            return {
                "success": True,
                "target_item_id": target_series.id,
                "target_item_name": target_series.name,
                "merged_count": len(source_ids),
                "total_movies_affected": len(all_movie_ids),
                "message": f"成功合并 {len(source_ids)} 个系列，影响 {len(all_movie_ids)} 部影片"
            }

        except Exception as e:
            self.db.rollback()
            logger.error(f"合并系列时发生错误: {e}")
            return {
                "success": False,
                "message": f"合并失败: {str(e)}"
            }


class ActorService:
    """演员管理服务"""

    def __init__(self, db: Session):
        self.db = db
        self.movie_update_service = MovieUpdateMarkService(db)
        self.name_resolver = NameConflictResolver(db)

    def create_actor(self, actor_data: ActorCreate) -> Optional[Actor]:
        """
        创建新演员

        Args:
            actor_data: 演员创建数据

        Returns:
            创建的演员对象，如果名称已存在则返回 None
        """
        try:
            # 检查演员名称是否已存在
            existing_actor = self.db.query(Actor).filter(Actor.name == actor_data.name).first()
            if existing_actor:
                return None

            # 创建新演员
            actor = Actor(
                name=actor_data.name,
                role=actor_data.role,
                biography=actor_data.biography,
                actor_type=actor_data.actor_type or "Actor"
            )

            self.db.add(actor)
            self.db.commit()
            self.db.refresh(actor)

            return actor

        except IntegrityError:
            self.db.rollback()
            logger.warning(f"演员名称 '{actor_data.name}' 已存在")
            return None
        except Exception as e:
            self.db.rollback()
            logger.error(f"创建演员时发生错误: {e}")
            return None



    def get_actors_with_movie_counts(self, limit: int = 50, offset: int = 0, search: Optional[str] = None) -> Tuple[List[Dict[str, Any]], int]:
        """
        获取演员列表及其电影数量（批量查询优化版本）

        Args:
            limit: 每页数量
            offset: 偏移量
            search: 搜索关键词

        Returns:
            (演员列表及电影数量, 总数量)
        """
        try:
            # 构建基础查询
            base_query = self.db.query(Actor)

            # 搜索过滤
            if search:
                search_pattern = f"%{search}%"
                base_query = base_query.filter(
                    or_(
                        Actor.name.ilike(search_pattern),
                        Actor.role.ilike(search_pattern),
                        Actor.biography.ilike(search_pattern)
                    )
                )

            # 获取总数量
            total_count = base_query.count()

            # 先获取所有符合条件的演员（不分页）
            all_actors = base_query.all()

            # 在应用层进行中文拼音排序
            def sort_actors(actor):
                # 主排序键：名称（支持中文拼音排序）
                name_key = chinese_sort_key(actor.name)
                # 次排序键：创建时间（倒序，所以取负值）
                time_key = -actor.created_at.timestamp() if actor.created_at else 0
                return (name_key, time_key)

            sorted_actors = sorted(all_actors, key=sort_actors)

            # 应用分页
            actors = sorted_actors[offset:offset + limit]

            if not actors:
                return [], total_count

            # 批量查询电影数量
            actor_ids = [actor.id for actor in actors]
            movie_counts = self.db.query(
                movie_actors.c.actor_id,
                func.count(movie_actors.c.movie_id).label('movie_count')
            ).filter(
                movie_actors.c.actor_id.in_(actor_ids)
            ).group_by(movie_actors.c.actor_id).all()

            # 构建电影数量映射
            count_map = {actor_id: count for actor_id, count in movie_counts}

            # 构建结果列表
            result = []
            for actor in actors:
                result.append({
                    'actor': actor,
                    'movie_count': count_map.get(actor.id, 0)
                })

            return result, total_count

        except Exception as e:
            logger.error(f"获取演员列表及电影数量时发生错误: {e}")
            return [], 0

    def get_actor_by_id(self, actor_id: int) -> Optional[Actor]:
        """根据 ID 获取演员"""
        try:
            return self.db.query(Actor).filter(Actor.id == actor_id).first()
        except Exception as e:
            logger.error(f"获取演员时发生错误: {e}")
            return None

    def update_actor(self, actor_id: int, update_data: ActorUpdate) -> Optional[Actor]:
        """
        更新演员信息

        Args:
            actor_id: 演员ID
            update_data: 更新数据

        Returns:
            更新后的演员对象，如果不存在则返回 None
        """
        try:
            actor = self.db.query(Actor).filter(Actor.id == actor_id).first()
            if not actor:
                return None

            # 解决名称冲突
            if hasattr(update_data, 'name') and update_data.name and update_data.name != actor.name:
                resolved_name = self.name_resolver.resolve_name_conflict(Actor, update_data.name, actor_id)
                if resolved_name != update_data.name:
                    logger.info(f"演员名称冲突已解决: '{update_data.name}' -> '{resolved_name}'")
                    update_data.name = resolved_name

            # 更新字段
            update_dict = update_data.model_dump(exclude_unset=True)
            for field, value in update_dict.items():
                setattr(actor, field, value)

            self.db.commit()
            self.db.refresh(actor)

            # 标记相关影片为已更新
            updated_movies_count = self.movie_update_service.mark_movies_updated_by_actor(actor_id)

            logger.info(f"成功更新演员: {actor.name} (ID: {actor.id})，标记了 {updated_movies_count} 部相关影片为已更新")
            return actor

        except Exception as e:
            self.db.rollback()
            logger.error(f"更新演员时发生错误: {e}")
            return None

    def delete_actor(self, actor_id: int, force: bool = False) -> Tuple[bool, str]:
        """
        删除演员

        Args:
            actor_id: 演员ID
            force: 是否强制删除（忽略关联关系）

        Returns:
            (是否成功, 消息)
        """
        try:
            actor = self.db.query(Actor).filter(Actor.id == actor_id).first()
            if not actor:
                return False, "演员不存在"

            # 检查关联的电影数量
            movie_count = self.db.query(Movie).join(Movie.actors).filter(Actor.id == actor_id).count()

            if movie_count > 0 and not force:
                return False, f"演员被 {movie_count} 部电影使用，无法删除。使用 force=true 强制删除。"

            # 在删除前标记相关影片为已更新
            updated_movies_count = 0
            if movie_count > 0:
                updated_movies_count = self.movie_update_service.mark_movies_updated_by_actor(actor_id)

            self.db.delete(actor)
            self.db.commit()

            message = f"成功删除演员: {actor.name}"
            if movie_count > 0:
                message += f"（强制删除，影响 {movie_count} 部电影，已标记 {updated_movies_count} 部影片为已更新）"

            logger.info(message)
            return True, message

        except Exception as e:
            self.db.rollback()
            logger.error(f"删除演员时发生错误: {e}")
            return False, f"删除失败: {str(e)}"

    def get_actor_with_movie_count(self, actor_id: int) -> Optional[Dict[str, Any]]:
        """获取演员及其关联的电影数量（优化版本）"""
        try:
            actor = self.get_actor_by_id(actor_id)
            if not actor:
                return None

            # 使用关联表直接查询，避免JOIN
            movie_count = self.db.query(movie_actors.c.movie_id).filter(
                movie_actors.c.actor_id == actor_id
            ).count()

            return {
                "actor": actor,
                "movie_count": movie_count
            }

        except Exception as e:
            logger.error(f"获取演员详情时发生错误: {e}")
            return None



    def batch_delete_actors(self, request: BatchDeleteRequest) -> BatchDeleteResult:
        """
        批量删除演员

        Args:
            request: 批量删除请求

        Returns:
            批量删除结果
        """
        success_ids = []
        failed_items = []

        for actor_id in request.ids:
            try:
                success, message = self.delete_actor(actor_id, force=request.force)
                if success:
                    success_ids.append(actor_id)
                else:
                    failed_items.append(BatchDeleteFailedItem(id=actor_id, error=message))
            except Exception as e:
                failed_items.append(BatchDeleteFailedItem(id=actor_id, error=f"删除失败: {str(e)}"))

        return BatchDeleteResult(
            total_count=len(request.ids),
            success_count=len(success_ids),
            failed_count=len(failed_items),
            success_ids=success_ids,
            failed_items=failed_items
        )

    def unified_delete_actors(self, request: UnifiedDeleteRequest) -> Union[Tuple[bool, str], BatchDeleteResult]:
        """
        统一删除演员（支持单项和批量）

        Args:
            request: 统一删除请求

        Returns:
            单项删除返回 (bool, str)，批量删除返回 BatchDeleteResult
        """
        if len(request.ids) == 1:
            # 单项删除
            return self.delete_actor(request.ids[0], force=request.force)
        else:
            # 批量删除
            batch_request = BatchDeleteRequest(ids=request.ids, force=request.force)
            return self.batch_delete_actors(batch_request)

    def check_rename_conflict(self, actor_id: int, new_name: str) -> Dict[str, Any]:
        """
        检查重命名是否会产生冲突

        Args:
            actor_id: 要重命名的演员ID
            new_name: 新名称

        Returns:
            冲突检查结果
        """
        try:
            # 检查演员是否存在
            actor = self.get_actor_by_id(actor_id)
            if not actor:
                return {
                    "has_conflict": False,
                    "can_auto_merge": False,
                    "message": "演员不存在"
                }

            # 如果新名称与当前名称相同，无冲突
            if actor.name == new_name:
                return {
                    "has_conflict": False,
                    "can_auto_merge": False,
                    "message": "名称未发生变化"
                }

            # 检查是否存在同名演员
            existing_actor = self.db.query(Actor).filter(
                Actor.name == new_name,
                Actor.id != actor_id
            ).first()

            if existing_actor:
                # 获取现有演员的影片数量（优化查询）
                movie_count = self.db.query(movie_actors.c.movie_id).filter(movie_actors.c.actor_id == existing_actor.id).count()

                return {
                    "has_conflict": True,
                    "can_auto_merge": True,
                    "conflict_info": {
                        "existing_item_id": existing_actor.id,
                        "existing_item_name": existing_actor.name,
                        "movie_count": movie_count
                    },
                    "message": f"演员名称 '{new_name}' 已存在，可以选择合并"
                }
            else:
                return {
                    "has_conflict": False,
                    "can_auto_merge": False,
                    "message": "可以安全重命名"
                }

        except Exception as e:
            logger.error(f"检查演员重命名冲突时发生错误: {e}")
            return {
                "has_conflict": False,
                "can_auto_merge": False,
                "message": f"检查失败: {str(e)}"
            }

    def check_merge_conflict(self, target_name: str, source_ids: List[int]) -> Dict[str, Any]:
        """
        检查合并是否会产生冲突

        Args:
            target_name: 目标名称
            source_ids: 源演员ID列表

        Returns:
            冲突检查结果
        """
        try:
            # 检查是否存在同名演员
            existing_actor = self.db.query(Actor).filter(Actor.name == target_name).first()

            if existing_actor:
                # 如果现有演员在源列表中，可以直接合并
                if existing_actor.id in source_ids:
                    return {
                        "can_merge": True,
                        "message": "目标名称对应的演员在合并列表中，可以直接合并"
                    }
                else:
                    # 获取现有演员的影片数量（优化查询）
                    movie_count = self.db.query(movie_actors.c.movie_id).filter(movie_actors.c.actor_id == existing_actor.id).count()

                    return {
                        "can_merge": False,
                        "conflict_info": {
                            "existing_item_id": existing_actor.id,
                            "existing_item_name": existing_actor.name,
                            "movie_count": movie_count
                        },
                        "message": f"演员名称 '{target_name}' 已存在，需要处理冲突"
                    }
            else:
                return {
                    "can_merge": True,
                    "message": "可以创建新演员进行合并"
                }

        except Exception as e:
            logger.error(f"检查演员合并冲突时发生错误: {e}")
            return {
                "can_merge": False,
                "message": f"检查失败: {str(e)}"
            }

    def merge_actors(self, source_ids: List[int], target_name: str, target_description: Optional[str] = None) -> Dict[str, Any]:
        """
        合并多个演员

        Args:
            source_ids: 要合并的源演员ID列表
            target_name: 目标演员名称
            target_description: 目标演员简介

        Returns:
            合并结果
        """
        try:
            # 验证源演员是否都存在
            source_actors = []
            for actor_id in source_ids:
                actor = self.get_actor_by_id(actor_id)
                if not actor:
                    return {
                        "success": False,
                        "message": f"演员 ID {actor_id} 不存在"
                    }
                source_actors.append(actor)

            # 检查是否存在目标名称的演员
            existing_target = self.db.query(Actor).filter(Actor.name == target_name).first()

            if existing_target and existing_target.id not in source_ids:
                return {
                    "success": False,
                    "message": f"目标名称 '{target_name}' 已存在且不在合并列表中"
                }

            # 收集所有关联的影片
            all_movie_ids = set()
            for actor in source_actors:
                movie_ids = self.db.query(Movie.id).join(Movie.actors).filter(Actor.id == actor.id).all()
                all_movie_ids.update([movie_id[0] for movie_id in movie_ids])

            # 确定目标演员
            if existing_target and existing_target.id in source_ids:
                # 使用现有演员作为目标
                target_actor = existing_target
                # 更新简介（如果提供）
                if target_description is not None:
                    target_actor.biography = target_description
                # 从源列表中移除目标演员
                source_actors = [actor for actor in source_actors if actor.id != target_actor.id]
            else:
                # 创建新的目标演员，使用第一个源演员的其他信息作为默认值
                first_actor = source_actors[0]
                target_actor = Actor(
                    name=target_name,
                    role=first_actor.role,  # 使用第一个演员的角色
                    biography=target_description or first_actor.biography,
                    actor_type=first_actor.actor_type
                )
                self.db.add(target_actor)
                self.db.flush()  # 获取ID但不提交

            # 将所有影片关联到目标演员
            for movie_id in all_movie_ids:
                movie = self.db.query(Movie).filter(Movie.id == movie_id).first()
                if movie and target_actor not in movie.actors:
                    movie.actors.append(target_actor)

            # 删除源演员（除了目标演员）
            deleted_count = 0
            for actor in source_actors:
                # 先移除所有关联关系
                actor.movies.clear()
                self.db.delete(actor)
                deleted_count += 1

            # 提交事务
            self.db.commit()
            self.db.refresh(target_actor)

            logger.info(f"成功合并 {len(source_ids)} 个演员到 '{target_name}' (ID: {target_actor.id})")

            return {
                "success": True,
                "target_item_id": target_actor.id,
                "target_item_name": target_actor.name,
                "merged_count": len(source_ids),
                "total_movies_affected": len(all_movie_ids),
                "message": f"成功合并 {len(source_ids)} 个演员，影响 {len(all_movie_ids)} 部影片"
            }

        except Exception as e:
            self.db.rollback()
            logger.error(f"合并演员时发生错误: {e}")
            return {
                "success": False,
                "message": f"合并失败: {str(e)}"
            }
