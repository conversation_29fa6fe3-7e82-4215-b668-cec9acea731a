"""
翻译服务模块
提供 ChatGPT API 调用、错误处理、重试机制等翻译功能
"""
import json
import asyncio
import aiohttp
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session
from app.services.config_service import ConfigService
import logging

logger = logging.getLogger(__name__)


class TranslationService:
    """翻译服务类"""
    
    # 配置键名常量
    CONFIG_KEY_TRANSLATION_API_URL = "translation_api_url"
    CONFIG_KEY_TRANSLATION_API_KEY = "translation_api_key"
    CONFIG_KEY_TRANSLATION_MODEL_NAME = "translation_model_name"
    CONFIG_KEY_TRANSLATION_PROMPT_TEMPLATE = "translation_prompt_template"
    CONFIG_KEY_TRANSLATION_SOURCE_LANGUAGE = "translation_source_language"
    CONFIG_KEY_TRANSLATION_TARGET_LANGUAGE = "translation_target_language"
    CONFIG_KEY_TRANSLATION_ENABLED = "translation_enabled"

    # 默认配置
    DEFAULT_API_URL = "https://api.openai.com/v1/chat/completions"
    DEFAULT_MODEL_NAME = "gpt-3.5-turbo"
    DEFAULT_PROMPT_TEMPLATE = "请将以下{source_language}文本翻译成{target_language}，保持原意不变，只返回翻译结果：\n\n{text}"
    DEFAULT_SOURCE_LANGUAGE = "自动检测"
    DEFAULT_TARGET_LANGUAGE = "中文"
    
    # 重试配置
    MAX_RETRIES = 3
    RETRY_DELAY = 1.0  # 秒
    REQUEST_TIMEOUT = 30  # 秒
    
    def __init__(self, db: Session):
        self.db = db
        self.config_service = ConfigService(db)
    
    def get_translation_config(self) -> Dict[str, Any]:
        """
        获取翻译配置
        
        Returns:
            翻译配置字典
        """
        try:
            config = {
                'api_url': self._get_config_value(self.CONFIG_KEY_TRANSLATION_API_URL, self.DEFAULT_API_URL),
                'api_key': self._get_config_value(self.CONFIG_KEY_TRANSLATION_API_KEY, ''),
                'model_name': self._get_config_value(self.CONFIG_KEY_TRANSLATION_MODEL_NAME, self.DEFAULT_MODEL_NAME),
                'prompt_template': self._get_config_value(self.CONFIG_KEY_TRANSLATION_PROMPT_TEMPLATE, self.DEFAULT_PROMPT_TEMPLATE),
                'source_language': self._get_config_value(self.CONFIG_KEY_TRANSLATION_SOURCE_LANGUAGE, self.DEFAULT_SOURCE_LANGUAGE),
                'target_language': self._get_config_value(self.CONFIG_KEY_TRANSLATION_TARGET_LANGUAGE, self.DEFAULT_TARGET_LANGUAGE),
                'enabled': self._get_config_value(self.CONFIG_KEY_TRANSLATION_ENABLED, 'false').lower() == 'true'
            }
            
            logger.debug("获取翻译配置成功")
            return config
            
        except Exception as e:
            logger.error(f"获取翻译配置时发生错误: {e}")
            return {
                'api_url': self.DEFAULT_API_URL,
                'api_key': '',
                'model_name': self.DEFAULT_MODEL_NAME,
                'prompt_template': self.DEFAULT_PROMPT_TEMPLATE,
                'source_language': self.DEFAULT_SOURCE_LANGUAGE,
                'target_language': self.DEFAULT_TARGET_LANGUAGE,
                'enabled': False
            }
    
    def set_translation_config(self, config: Dict[str, Any]) -> bool:
        """
        设置翻译配置
        
        Args:
            config: 翻译配置字典
            
        Returns:
            是否设置成功
        """
        try:
            success = True
            
            # 设置各个配置项
            if 'api_url' in config:
                result = self.config_service.set_config(
                    self.CONFIG_KEY_TRANSLATION_API_URL,
                    config['api_url'],
                    "ChatGPT API 兼容 URL"
                )
                success = success and result is not None
            
            if 'api_key' in config:
                result = self.config_service.set_config(
                    self.CONFIG_KEY_TRANSLATION_API_KEY,
                    config['api_key'],
                    "ChatGPT API Key"
                )
                success = success and result is not None

            if 'model_name' in config:
                result = self.config_service.set_config(
                    self.CONFIG_KEY_TRANSLATION_MODEL_NAME,
                    config['model_name'],
                    "翻译模型名称"
                )
                success = success and result is not None

            if 'prompt_template' in config:
                result = self.config_service.set_config(
                    self.CONFIG_KEY_TRANSLATION_PROMPT_TEMPLATE,
                    config['prompt_template'],
                    "翻译提示词模板"
                )
                success = success and result is not None

            if 'source_language' in config:
                result = self.config_service.set_config(
                    self.CONFIG_KEY_TRANSLATION_SOURCE_LANGUAGE,
                    config['source_language'],
                    "翻译源语言"
                )
                success = success and result is not None

            if 'target_language' in config:
                result = self.config_service.set_config(
                    self.CONFIG_KEY_TRANSLATION_TARGET_LANGUAGE,
                    config['target_language'],
                    "翻译目标语言"
                )
                success = success and result is not None
            
            if 'enabled' in config:
                result = self.config_service.set_config(
                    self.CONFIG_KEY_TRANSLATION_ENABLED,
                    str(config['enabled']).lower(),
                    "是否启用翻译功能"
                )
                success = success and result is not None
            
            if success:
                logger.debug("翻译配置设置成功")
            else:
                logger.error("翻译配置设置失败")
            
            return success
            
        except Exception as e:
            logger.error(f"设置翻译配置时发生错误: {e}")
            return False
    
    async def translate_text(self, text: str, field_name: str = "") -> Dict[str, Any]:
        """
        翻译文本
        
        Args:
            text: 要翻译的文本
            field_name: 字段名称（用于日志）
            
        Returns:
            翻译结果字典，包含 success、translated_text、error 等字段
        """
        try:
            # 获取翻译配置
            config = self.get_translation_config()
            
            # 检查翻译功能是否启用
            if not config['enabled']:
                return {
                    'success': False,
                    'error': '翻译功能未启用',
                    'translated_text': text
                }
            
            # 检查必要配置
            if not config['api_key']:
                return {
                    'success': False,
                    'error': 'API Key 未配置',
                    'translated_text': text
                }
            
            if not text or not text.strip():
                return {
                    'success': False,
                    'error': '文本内容为空',
                    'translated_text': text
                }
            
            # 构建提示词
            prompt = config['prompt_template'].format(
                source_language=config['source_language'],
                target_language=config['target_language'],
                text=text.strip()
            )
            
            # 调用 ChatGPT API
            result = await self._call_chatgpt_api(
                api_url=config['api_url'],
                api_key=config['api_key'],
                model_name=config['model_name'],
                prompt=prompt
            )
            
            if result['success']:
                logger.info(f"翻译成功 - 字段: {field_name}, 原文: {text[:50]}...")
                return {
                    'success': True,
                    'translated_text': result['response'],
                    'original_text': text
                }
            else:
                logger.error(f"翻译失败 - 字段: {field_name}, 错误: {result['error']}")
                return {
                    'success': False,
                    'error': result['error'],
                    'translated_text': text
                }
                
        except Exception as e:
            logger.error(f"翻译文本时发生错误: {e}")
            return {
                'success': False,
                'error': f'翻译服务异常: {str(e)}',
                'translated_text': text
            }
    
    async def translate_multiple_fields(self, fields: Dict[str, str]) -> Dict[str, Dict[str, Any]]:
        """
        批量翻译多个字段
        
        Args:
            fields: 字段字典，键为字段名，值为要翻译的文本
            
        Returns:
            翻译结果字典，键为字段名，值为翻译结果
        """
        try:
            results = {}
            
            # 并发翻译所有字段
            tasks = []
            field_names = []
            
            for field_name, text in fields.items():
                if text and text.strip():
                    tasks.append(self.translate_text(text, field_name))
                    field_names.append(field_name)
                else:
                    # 空文本直接返回原值
                    results[field_name] = {
                        'success': True,
                        'translated_text': text,
                        'original_text': text
                    }
            
            # 等待所有翻译任务完成
            if tasks:
                translation_results = await asyncio.gather(*tasks, return_exceptions=True)
                
                for i, result in enumerate(translation_results):
                    field_name = field_names[i]
                    if isinstance(result, Exception):
                        logger.error(f"翻译字段 {field_name} 时发生异常: {result}")
                        results[field_name] = {
                            'success': False,
                            'error': f'翻译异常: {str(result)}',
                            'translated_text': fields[field_name]
                        }
                    else:
                        results[field_name] = result
            
            logger.info(f"批量翻译完成，共处理 {len(fields)} 个字段")
            return results
            
        except Exception as e:
            logger.error(f"批量翻译时发生错误: {e}")
            # 返回所有字段的错误结果
            return {
                field_name: {
                    'success': False,
                    'error': f'批量翻译异常: {str(e)}',
                    'translated_text': text
                }
                for field_name, text in fields.items()
            }
    
    def _normalize_api_url(self, api_url: str) -> str:
        """
        标准化 API URL，确保指向正确的 chat completions 端点

        Args:
            api_url: 原始 API URL

        Returns:
            标准化后的 API URL
        """
        api_url = api_url.strip().rstrip('/')

        # 如果 URL 不包含 chat/completions，则自动添加
        if not api_url.endswith('/chat/completions'):
            if api_url.endswith('/v1'):
                api_url += '/chat/completions'
            elif api_url.endswith('/api/v1'):
                api_url += '/chat/completions'
            else:
                # 如果是基础 URL，添加完整路径
                api_url += '/v1/chat/completions'

        return api_url

    async def _call_chatgpt_api(self, api_url: str, api_key: str, model_name: str, prompt: str) -> Dict[str, Any]:
        """
        调用 ChatGPT API

        Args:
            api_url: API URL
            api_key: API Key
            model_name: 模型名称
            prompt: 提示词

        Returns:
            API 调用结果
        """
        # 标准化 API URL
        normalized_url = self._normalize_api_url(api_url)
        logger.debug(f"使用标准化 URL: {normalized_url}")

        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }

        payload = {
            'model': model_name,
            'messages': [
                {
                    'role': 'user',
                    'content': prompt
                }
            ],
            'max_tokens': 1000,
            'temperature': 0.3
        }
        
        for attempt in range(self.MAX_RETRIES):
            try:
                timeout = aiohttp.ClientTimeout(total=self.REQUEST_TIMEOUT)
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    async with session.post(normalized_url, headers=headers, json=payload) as response:
                        if response.status == 200:
                            data = await response.json()
                            if 'choices' in data and len(data['choices']) > 0:
                                content = data['choices'][0]['message']['content'].strip()
                                return {
                                    'success': True,
                                    'response': content
                                }
                            else:
                                return {
                                    'success': False,
                                    'error': 'API 响应格式异常'
                                }
                        else:
                            error_text = await response.text()

                            # 为常见错误提供更友好的提示
                            if response.status == 401:
                                if 'openrouter.ai' in normalized_url.lower():
                                    error_msg = f'OpenRouter API 认证失败 (状态码: {response.status}): 请检查 API Key 是否正确。OpenRouter API Key 格式通常以 "sk-or-" 开头。详细错误: {error_text}'
                                else:
                                    error_msg = f'API 认证失败 (状态码: {response.status}): 请检查 API Key 是否正确。详细错误: {error_text}'
                            elif response.status == 403:
                                error_msg = f'API 访问被拒绝 (状态码: {response.status}): 请检查 API Key 权限或账户余额。详细错误: {error_text}'
                            elif response.status == 429:
                                error_msg = f'API 请求频率限制 (状态码: {response.status}): 请稍后重试。详细错误: {error_text}'
                            else:
                                error_msg = f'API 请求失败 (状态码: {response.status}): {error_text}'

                            return {
                                'success': False,
                                'error': error_msg
                            }
                            
            except asyncio.TimeoutError:
                if attempt < self.MAX_RETRIES - 1:
                    logger.warning(f"API 请求超时，正在重试 ({attempt + 1}/{self.MAX_RETRIES})")
                    await asyncio.sleep(self.RETRY_DELAY * (attempt + 1))
                    continue
                else:
                    return {
                        'success': False,
                        'error': 'API 请求超时'
                    }
            except Exception as e:
                error_msg = f"API 请求异常: {str(e)}, URL: {normalized_url}"
                if attempt < self.MAX_RETRIES - 1:
                    logger.warning(f"{error_msg}，正在重试 ({attempt + 1}/{self.MAX_RETRIES})")
                    await asyncio.sleep(self.RETRY_DELAY * (attempt + 1))
                    continue
                else:
                    logger.error(f"API 请求最终失败: {error_msg}")
                    return {
                        'success': False,
                        'error': error_msg
                    }
        
        return {
            'success': False,
            'error': '达到最大重试次数'
        }
    
    def _get_config_value(self, config_key: str, default_value: str) -> str:
        """
        获取配置值
        
        Args:
            config_key: 配置键名
            default_value: 默认值
            
        Returns:
            配置值
        """
        try:
            config = self.config_service.get_config(config_key)
            return config.config_value if config and config.config_value else default_value
        except Exception as e:
            logger.error(f"获取配置值时发生错误: {e}")
            return default_value
