/**
 * 设置页面脚本
 * 处理系统设置和映射规则管理
 */

class SettingsManager {
    constructor() {
        // 当前状态
        this.currentTab = 'mappings';
        this.currentMappingType = 'tags';
        this.mappingRules = [];
        this.selectedMappingIds = new Set();
        this.isLoading = false;
        this.searchTimeout = null;
        
        // 分页参数
        this.currentPage = 1;
        this.pageSize = 20;
        this.totalCount = 0;
        
        // 搜索和过滤参数
        this.searchQuery = '';
        this.statusFilter = '';
        
        // 当前编辑的映射规则
        this.currentMappingRule = null;

        // 视频扩展名配置状态
        this.currentVideoExtensions = [];
        this.defaultVideoExtensions = [];
        this.isLoadingExtensions = false;

        // 翻译配置状态
        this.translationConfig = {
            enabled: false,
            api_url: '',
            api_key: '',
            prompt_template: '',
            target_language: '中文'
        };
        this.isLoadingTranslationConfig = false;

        // 映射类型配置
        this.mappingTypeConfig = {
            tags: {
                name: '标签',
                icon: 'bi-tags',
                title: '标签映射列表'
            },
            genres: {
                name: '分类',
                icon: 'bi-collection',
                title: '分类映射列表'
            },
            series: {
                name: '系列',
                icon: 'bi-film',
                title: '系列映射列表'
            },
            actors: {
                name: '演员',
                icon: 'bi-people',
                title: '演员映射列表'
            }
        };
    }

    /**
     * 初始化设置管理器
     */
    async init() {
        this.bindEvents();
        this.initializeTabs();
        this.initializeBatchDeleteButton();
        await this.loadMappingRules();
        await this.loadVideoExtensions();
        await this.loadTranslationConfig();
    }

    /**
     * 初始化批量删除按钮状态
     */
    initializeBatchDeleteButton() {
        const batchDeleteBtn = document.getElementById('batch-delete-mapping-btn');
        if (batchDeleteBtn) {
            const parentLi = batchDeleteBtn.closest('li');
            if (parentLi) {
                parentLi.style.display = 'none'; // 初始隐藏
            }
        }
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 标签页切换
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                e.preventDefault();
                const tabName = tab.getAttribute('data-tab');
                this.switchTab(tabName);
            });
        });

        // 映射类型切换
        document.querySelectorAll('[data-mapping-type]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const mappingType = btn.getAttribute('data-mapping-type');
                this.switchMappingType(mappingType);
            });
        });

        // 搜索输入
        const searchInput = document.getElementById('mapping-search-input');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                clearTimeout(this.searchTimeout);

                // 立即清空表格内容，避免显示旧数据
                this.clearMappingTableContent();

                this.searchTimeout = setTimeout(() => {
                    this.searchQuery = e.target.value.trim();
                    this.currentPage = 1;
                    this.loadMappingRules();
                }, 300);
            });
        }

        // 状态过滤
        const statusFilter = document.getElementById('mapping-status-filter');
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.statusFilter = e.target.value;
                this.currentPage = 1;
                this.loadMappingRules();
            });
        }

        // 操作按钮
        this.bindMappingButtons();
        this.bindModalEvents();
        this.bindTranslationEvents();
    }

    /**
     * 绑定映射相关按钮事件
     */
    bindMappingButtons() {
        // 添加映射按钮
        const addBtn = document.getElementById('add-mapping-btn');
        const addFirstBtn = document.getElementById('add-first-mapping-btn');
        if (addBtn) addBtn.addEventListener('click', () => this.showAddMappingModal());
        if (addFirstBtn) addFirstBtn.addEventListener('click', () => this.showAddMappingModal());

        // 刷新按钮
        const refreshBtn = document.getElementById('refresh-mapping-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.loadMappingRules();
            });
        }

        // 重试按钮
        const retryBtn = document.getElementById('retry-mapping-btn');
        if (retryBtn) {
            retryBtn.addEventListener('click', () => {
                this.loadMappingRules();
            });
        }

        // 导入导出按钮
        const importBtn = document.getElementById('import-mapping-btn');
        const exportBtn = document.getElementById('export-mapping-btn');
        if (importBtn) importBtn.addEventListener('click', () => this.showImportModal());
        if (exportBtn) exportBtn.addEventListener('click', () => this.exportMappingRules());

        // 批量删除按钮
        const batchDeleteBtn = document.getElementById('batch-delete-mapping-btn');
        if (batchDeleteBtn) {
            batchDeleteBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.showBatchDeleteModal();
            });
        }

        // 全选复选框
        const selectAllCheckbox = document.getElementById('mapping-select-all');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => {
                this.toggleSelectAll(e.target.checked);
            });
        }

        // 翻译按钮事件绑定
        this.bindMappingTranslationEvents();
    }

    /**
     * 绑定模态框事件
     */
    bindModalEvents() {
        // 映射表单提交
        const mappingForm = document.getElementById('mapping-form');
        if (mappingForm) {
            mappingForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleMappingFormSubmit();
            });
        }

        // 删除确认
        const confirmDeleteBtn = document.getElementById('confirm-delete-mapping-btn');
        if (confirmDeleteBtn) {
            confirmDeleteBtn.addEventListener('click', () => {
                this.handleDeleteMapping();
            });
        }

        // 批量删除确认
        const confirmBatchDeleteBtn = document.getElementById('confirm-batch-delete-mapping-btn');
        if (confirmBatchDeleteBtn) {
            confirmBatchDeleteBtn.addEventListener('click', () => {
                this.handleBatchDeleteMapping();
            });
        }

        // 文件导入
        const fileInput = document.getElementById('mapping-file-input');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => {
                this.handleFileSelect(e.target.files[0]);
            });
        }

        // 导入提交
        const importSubmitBtn = document.getElementById('import-mapping-submit-btn');
        if (importSubmitBtn) {
            importSubmitBtn.addEventListener('click', () => {
                this.handleImportSubmit();
            });
        }

        // 映射表单实时验证
        const originalValueInput = document.getElementById('mapping-original-value');
        const mappedValueInput = document.getElementById('mapping-mapped-value');
        if (originalValueInput && mappedValueInput) {
            const validateMapping = () => {
                this.validateMappingValues(originalValueInput.value.trim(), mappedValueInput.value.trim());
            };

            originalValueInput.addEventListener('input', validateMapping);
            mappedValueInput.addEventListener('input', validateMapping);
        }

        // 视频扩展名配置事件
        const addExtensionBtn = document.getElementById('add-extension-btn');
        const newExtensionInput = document.getElementById('new-extension-input');
        if (addExtensionBtn && newExtensionInput) {
            addExtensionBtn.addEventListener('click', () => {
                this.handleAddExtension();
            });

            newExtensionInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.handleAddExtension();
                }
            });
        }



        const reloadExtensionsBtn = document.getElementById('reload-extensions-btn');
        if (reloadExtensionsBtn) {
            reloadExtensionsBtn.addEventListener('click', () => {
                this.loadVideoExtensions();
            });
        }
    }

    /**
     * 初始化标签页
     */
    initializeTabs() {
        // 从URL参数获取初始标签页
        const urlParams = new URLSearchParams(window.location.search);
        const tab = urlParams.get('tab') || 'mappings';
        this.switchTab(tab);
    }

    /**
     * 切换标签页
     */
    switchTab(tabName) {
        // 更新标签页状态
        document.querySelectorAll('.tab').forEach(tab => {
            tab.classList.remove('tab-active');
            if (tab.getAttribute('data-tab') === tabName) {
                tab.classList.add('tab-active');
            }
        });

        // 显示对应面板
        document.querySelectorAll('.tab-panel').forEach(panel => {
            panel.classList.add('hidden');
        });
        
        const targetPanel = document.getElementById(`${tabName}-panel`);
        if (targetPanel) {
            targetPanel.classList.remove('hidden');
        }

        this.currentTab = tabName;

        // 更新URL参数
        const url = new URL(window.location);
        url.searchParams.set('tab', tabName);
        window.history.replaceState({}, '', url);

        // 如果切换到映射标签页，加载数据
        if (tabName === 'mappings') {
            this.loadMappingRules();
        }

        // 如果切换到翻译配置标签页，检查状态
        if (tabName === 'translation') {
            this.checkTranslationStatus();
        }
    }

    /**
     * 切换映射类型
     */
    switchMappingType(mappingType) {
        // 更新按钮状态
        document.querySelectorAll('[data-mapping-type]').forEach(btn => {
            btn.classList.remove('btn-active');
            if (btn.getAttribute('data-mapping-type') === mappingType) {
                btn.classList.add('btn-active');
            }
        });

        this.currentMappingType = mappingType;
        this.currentPage = 1;
        this.selectedMappingIds.clear();

        // 立即清空表格内容，避免显示旧数据
        const tableBody = document.getElementById('mapping-table-body');
        if (tableBody) {
            tableBody.innerHTML = '';
        }

        // 隐藏所有状态，准备显示新的加载状态
        this.hideMappingStates();

        // 更新表格标题
        const config = this.mappingTypeConfig[mappingType];
        const tableTitle = document.getElementById('mapping-table-title');
        if (tableTitle && config) {
            tableTitle.textContent = config.title;
        }

        // 重新加载数据
        this.loadMappingRules();
    }

    /**
     * 加载映射规则列表
     */
    async loadMappingRules() {
        if (this.isLoading) return;

        this.isLoading = true;
        this.showMappingLoading();
        this.hideMappingStates();

        try {
            const params = {
                limit: this.pageSize,
                offset: (this.currentPage - 1) * this.pageSize
            };

            // 添加搜索参数
            if (this.searchQuery) {
                params.search = this.searchQuery;
            }

            // 添加状态过滤参数
            if (this.statusFilter) {
                params.status = this.statusFilter;
            }

            const response = await api.getMappingRules(this.currentMappingType, params);
            
            this.mappingRules = response.data || [];
            this.totalCount = response.total_count || 0;

            this.updateMappingDisplay();
            this.updateMappingPagination();
            this.updateMappingStatistics();

        } catch (error) {
            console.error('加载映射规则失败:', error);
            const errorMessage = handleApiError(error, '加载映射规则', false);
            toast.error(errorMessage);
            this.showMappingErrorState(errorMessage);
        } finally {
            this.isLoading = false;
            this.hideMappingLoading();
        }
    }

    /**
     * 更新映射规则显示
     */
    updateMappingDisplay() {
        const tableBody = document.getElementById('mapping-table-body');
        if (!tableBody) return;

        if (this.mappingRules.length === 0) {
            this.showMappingEmptyState();
            return;
        }

        // 隐藏加载状态和其他状态，显示表格内容
        this.hideMappingLoading();
        this.hideMappingEmptyState();
        this.hideMappingErrorState();

        const mappingHtml = this.mappingRules.map(rule => this.createMappingTableRow(rule)).join('');
        tableBody.innerHTML = mappingHtml;

        // 绑定行事件
        this.bindMappingRowEvents();
    }

    /**
     * 创建映射表格行
     */
    createMappingTableRow(rule) {
        const isSelected = this.selectedMappingIds.has(rule.id);

        // 根据业务逻辑更新状态显示：
        // - 有映射值：转换映射
        // - 无映射值：删除原始值
        let statusBadge, mappedValue;
        if (rule.mapped_value) {
            statusBadge = '<span class="badge badge-success">映射</span>';
            mappedValue = `<span class="font-medium">${utils.escapeHtml(rule.mapped_value)}</span>`;
        } else {
            statusBadge = '<span class="badge badge-error">删除</span>';
            mappedValue = '<span class="text-error italic">删除</span>';
        }

        return `
            <tr class="hover">
                <td>
                    <input type="checkbox" class="checkbox checkbox-primary mapping-row-checkbox"
                           data-mapping-id="${rule.id}" ${isSelected ? 'checked' : ''}>
                </td>
                <td>
                    <div class="font-medium">${utils.escapeHtml(rule.original_value)}</div>
                </td>
                <td>
                    <div class="max-w-xs truncate">${mappedValue}</div>
                </td>
                <td>${statusBadge}</td>
                <td>
                    <div class="text-sm text-base-content/60">
                        ${utils.formatDateTime(rule.created_at)}
                    </div>
                </td>
                <td>
                    <div class="text-sm text-base-content/60">
                        ${utils.formatDateTime(rule.updated_at)}
                    </div>
                </td>
                <td>
                    <div class="flex gap-1">
                        <button class="btn btn-ghost btn-xs edit-mapping-btn" data-mapping-id="${rule.id}" title="编辑映射规则">
                            <i class="bi bi-pencil" aria-label="编辑图标"></i>
                        </button>
                        <button class="btn btn-ghost btn-xs text-error delete-mapping-btn" data-mapping-id="${rule.id}" title="删除映射规则">
                            <i class="bi bi-trash" aria-label="删除图标"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    /**
     * 绑定映射表格行事件
     */
    bindMappingRowEvents() {
        // 复选框事件
        document.querySelectorAll('.mapping-row-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                const mappingId = parseInt(e.target.getAttribute('data-mapping-id'));
                if (e.target.checked) {
                    this.selectedMappingIds.add(mappingId);
                } else {
                    this.selectedMappingIds.delete(mappingId);
                }
                this.updateMappingSelectionUI();
            });
        });

        // 编辑按钮事件
        document.querySelectorAll('.edit-mapping-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                const mappingId = parseInt(e.target.closest('button').getAttribute('data-mapping-id'));
                this.showEditMappingModal(mappingId);
            });
        });

        // 删除按钮事件
        document.querySelectorAll('.delete-mapping-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                const mappingId = parseInt(e.target.closest('button').getAttribute('data-mapping-id'));
                this.showDeleteMappingModal(mappingId);
            });
        });
    }

    /**
     * 更新映射选择UI
     */
    updateMappingSelectionUI() {
        const selectedCount = this.selectedMappingIds.size;
        const totalCount = this.mappingRules.length;

        // 更新全选复选框状态
        const selectAllCheckbox = document.getElementById('mapping-select-all');
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = selectedCount > 0 && selectedCount === totalCount;
            selectAllCheckbox.indeterminate = selectedCount > 0 && selectedCount < totalCount;
        }

        // 更新统计信息
        const selectedMappingsElement = document.getElementById('selected-mappings');
        if (selectedMappingsElement) {
            selectedMappingsElement.textContent = selectedCount;
        }

        // 显示/隐藏批量操作按钮（在下拉菜单中）
        const batchDeleteBtn = document.getElementById('batch-delete-mapping-btn');
        if (batchDeleteBtn) {
            const parentLi = batchDeleteBtn.closest('li');
            if (parentLi) {
                parentLi.style.display = selectedCount > 0 ? 'block' : 'none';
            }
        }
    }

    /**
     * 切换全选状态
     */
    toggleSelectAll(checked) {
        this.selectedMappingIds.clear();

        if (checked) {
            this.mappingRules.forEach(rule => {
                this.selectedMappingIds.add(rule.id);
            });
        }

        // 更新复选框状态
        document.querySelectorAll('.mapping-row-checkbox').forEach(checkbox => {
            checkbox.checked = checked;
        });

        this.updateMappingSelectionUI();
    }

    /**
     * 更新映射统计信息
     */
    updateMappingStatistics() {
        const totalMappings = this.totalCount;
        // 根据新的业务逻辑：有映射值的是转换映射，无映射值的是删除规则
        const convertMappings = this.mappingRules.filter(rule => rule.mapped_value).length;
        const deleteMappings = this.mappingRules.filter(rule => !rule.mapped_value).length;

        // 更新统计显示
        const totalElement = document.getElementById('total-mappings');
        const activeElement = document.getElementById('active-mappings');
        const emptyElement = document.getElementById('empty-mappings');

        if (totalElement) totalElement.textContent = totalMappings;
        if (activeElement) activeElement.textContent = convertMappings;
        if (emptyElement) emptyElement.textContent = deleteMappings;

        // 更新计数信息
        const countInfo = document.getElementById('mapping-count-info');
        if (countInfo) {
            const config = this.mappingTypeConfig[this.currentMappingType];
            countInfo.textContent = `共 ${totalMappings} 个${config ? config.name : ''}映射规则`;
        }
    }

    /**
     * 更新映射分页
     */
    updateMappingPagination() {
        const container = document.getElementById('mapping-pagination-container');
        if (!container) return;

        const totalPages = Math.ceil(this.totalCount / this.pageSize);

        if (totalPages <= 1) {
            container.innerHTML = '';
            return;
        }

        let paginationHtml = '<div class="join">';

        // 上一页按钮
        const prevDisabled = this.currentPage === 1 ? 'btn-disabled' : '';
        paginationHtml += `
            <button class="join-item btn btn-sm ${prevDisabled}" data-page="${this.currentPage - 1}">
                <i class="bi bi-chevron-left" aria-label="上一页图标"></i>
            </button>
        `;

        // 页码按钮
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(totalPages, this.currentPage + 2);

        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === this.currentPage ? 'btn-active' : '';
            paginationHtml += `
                <button class="join-item btn btn-sm ${activeClass}" data-page="${i}">
                    ${i}
                </button>
            `;
        }

        // 下一页按钮
        const nextDisabled = this.currentPage === totalPages ? 'btn-disabled' : '';
        paginationHtml += `
            <button class="join-item btn btn-sm ${nextDisabled}" data-page="${this.currentPage + 1}">
                <i class="bi bi-chevron-right" aria-label="下一页图标"></i>
            </button>
        `;

        paginationHtml += '</div>';
        container.innerHTML = paginationHtml;

        // 绑定分页事件
        container.querySelectorAll('button[data-page]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const page = parseInt(e.target.closest('button').getAttribute('data-page'));
                if (page >= 1 && page <= totalPages && page !== this.currentPage) {
                    this.currentPage = page;
                    this.loadMappingRules();
                }
            });
        });
    }

    // ========== 验证方法 ==========

    /**
     * 清除验证状态
     */
    clearValidationState() {
        const warningElement = document.getElementById('mapping-validation-warning');
        const submitBtn = document.getElementById('mapping-submit-btn');

        if (warningElement) {
            warningElement.classList.add('hidden');
        }

        if (submitBtn) {
            submitBtn.disabled = false;
        }
    }

    /**
     * 验证映射值
     */
    validateMappingValues(originalValue, mappedValue) {
        const warningElement = document.getElementById('mapping-validation-warning');
        const submitBtn = document.getElementById('mapping-submit-btn');

        // 清除之前的警告
        if (warningElement) {
            warningElement.classList.add('hidden');
        }

        // 启用提交按钮
        if (submitBtn) {
            submitBtn.disabled = false;
        }

        // 检查是否相同
        if (originalValue && mappedValue && originalValue === mappedValue) {
            if (warningElement) {
                warningElement.textContent = '原始值和映射值不能相同';
                warningElement.classList.remove('hidden');
            }

            // 禁用提交按钮
            if (submitBtn) {
                submitBtn.disabled = true;
            }
        }
    }

    // ========== 状态管理方法 ==========

    /**
     * 显示映射加载状态
     */
    showMappingLoading() {
        const loadingState = document.getElementById('mapping-loading-state');
        if (loadingState) loadingState.classList.remove('hidden');
    }

    /**
     * 隐藏映射加载状态
     */
    hideMappingLoading() {
        const loadingState = document.getElementById('mapping-loading-state');
        if (loadingState) loadingState.classList.add('hidden');
    }

    /**
     * 显示映射空状态
     */
    showMappingEmptyState() {
        const emptyState = document.getElementById('mapping-empty-state');
        const loadingState = document.getElementById('mapping-loading-state');
        const errorState = document.getElementById('mapping-error-state');

        if (emptyState) emptyState.classList.remove('hidden');
        if (loadingState) loadingState.classList.add('hidden');
        if (errorState) errorState.classList.add('hidden');
    }

    /**
     * 隐藏映射空状态
     */
    hideMappingEmptyState() {
        const emptyState = document.getElementById('mapping-empty-state');
        if (emptyState) emptyState.classList.add('hidden');
    }

    /**
     * 显示映射错误状态
     */
    showMappingErrorState(message) {
        const errorState = document.getElementById('mapping-error-state');
        const errorMessage = document.getElementById('mapping-error-message');

        if (errorMessage) {
            errorMessage.textContent = message || '加载映射规则时发生错误，请稍后重试。';
        }

        if (errorState) {
            errorState.classList.remove('hidden');
        }

        console.error('映射加载错误:', message);
    }

    /**
     * 隐藏映射错误状态
     */
    hideMappingErrorState() {
        const errorState = document.getElementById('mapping-error-state');
        if (errorState) errorState.classList.add('hidden');
    }

    /**
     * 隐藏所有映射状态
     */
    hideMappingStates() {
        this.hideMappingLoading();
        this.hideMappingEmptyState();
        this.hideMappingErrorState();
    }

    /**
     * 清空映射表格内容
     */
    clearMappingTableContent() {
        const tableBody = document.getElementById('mapping-table-body');
        if (tableBody) {
            tableBody.innerHTML = '';
        }
    }

    // ========== 模态框处理方法 ==========

    /**
     * 显示添加映射模态框
     */
    showAddMappingModal() {
        this.currentMappingRule = null;

        const modal = document.getElementById('mapping-modal');
        const title = document.getElementById('mapping-modal-title');
        const form = document.getElementById('mapping-form');

        if (title) {
            const config = this.mappingTypeConfig[this.currentMappingType];
            title.textContent = `添加${config ? config.name : ''}映射规则`;
        }

        if (form) form.reset();

        // 清除验证状态
        this.clearValidationState();

        // 设置映射类型
        const mappingTypeInput = document.getElementById('mapping-type');
        if (mappingTypeInput) mappingTypeInput.value = this.currentMappingType;

        if (modal) {
            modal.showModal();

            // 检查并更新翻译按钮状态
            this.checkAndUpdateMappingTranslationButtons().catch(error => {
                console.error('更新映射翻译按钮状态失败:', error);
            });
        }
    }

    /**
     * 显示编辑映射模态框
     */
    showEditMappingModal(mappingId) {
        const rule = this.mappingRules.find(r => r.id === mappingId);
        if (!rule) {
            console.error('未找到映射规则，ID:', mappingId);
            toast.error('未找到要编辑的映射规则');
            return;
        }

        this.currentMappingRule = rule;

        const modal = document.getElementById('mapping-modal');
        const title = document.getElementById('mapping-modal-title');
        const form = document.getElementById('mapping-form');

        if (title) {
            const config = this.mappingTypeConfig[this.currentMappingType];
            title.textContent = `编辑${config ? config.name : ''}映射规则`;
        }

        // 填充表单数据
        const mappingIdInput = document.getElementById('mapping-id');
        const mappingTypeInput = document.getElementById('mapping-type');
        const originalValueInput = document.getElementById('mapping-original-value');
        const mappedValueInput = document.getElementById('mapping-mapped-value');

        if (mappingIdInput) mappingIdInput.value = rule.id;
        if (mappingTypeInput) mappingTypeInput.value = this.currentMappingType;
        if (originalValueInput) originalValueInput.value = rule.original_value;
        if (mappedValueInput) mappedValueInput.value = rule.mapped_value || '';

        // 清除验证状态并重新验证
        this.clearValidationState();
        this.validateMappingValues(rule.original_value, rule.mapped_value || '');

        if (modal) {
            modal.showModal();

            // 检查并更新翻译按钮状态
            this.checkAndUpdateMappingTranslationButtons().catch(error => {
                console.error('更新映射翻译按钮状态失败:', error);
            });
        } else {
            console.error('模态框元素不存在');
        }
    }

    /**
     * 处理映射表单提交
     */
    async handleMappingFormSubmit() {
        const form = document.getElementById('mapping-form');
        const submitBtn = document.getElementById('mapping-submit-btn');
        const spinner = submitBtn?.querySelector('.loading');

        if (!form) return;

        // 获取表单数据
        const formData = new FormData(form);
        const data = {
            original_value: formData.get('original_value')?.trim(),
            mapped_value: formData.get('mapped_value')?.trim() || null
        };

        // 验证数据
        if (!data.original_value) {
            toast.error('请输入原始值');
            return;
        }

        // 验证原始值和映射值不能相同
        if (data.mapped_value && data.original_value === data.mapped_value) {
            toast.error('原始值和映射值不能相同');
            return;
        }

        // 显示加载状态
        if (submitBtn) submitBtn.disabled = true;
        if (spinner) spinner.classList.remove('hidden');

        try {
            const mappingId = document.getElementById('mapping-id')?.value;

            if (mappingId && this.currentMappingRule) {
                // 更新映射规则
                await api.updateMappingRule(this.currentMappingType, parseInt(mappingId), data);
                toast.success('映射规则更新成功');
            } else {
                // 创建映射规则
                await api.createMappingRule(this.currentMappingType, data);
                toast.success('映射规则创建成功');
            }

            // 关闭模态框并刷新数据
            const modal = document.getElementById('mapping-modal');
            if (modal) modal.close();

            await this.loadMappingRules();

        } catch (error) {
            console.error('保存映射规则失败:', error);
            handleApiError(error, '保存映射规则');
        } finally {
            // 恢复按钮状态
            if (submitBtn) submitBtn.disabled = false;
            if (spinner) spinner.classList.add('hidden');
        }
    }

    /**
     * 显示删除映射确认模态框
     */
    showDeleteMappingModal(mappingId) {
        const rule = this.mappingRules.find(r => r.id === mappingId);
        if (!rule) return;

        this.currentMappingRule = rule;

        const modal = document.getElementById('delete-mapping-modal');
        const message = document.getElementById('delete-mapping-message');

        if (message) {
            message.textContent = `您确定要删除映射规则 "${rule.original_value}" 吗？`;
        }

        if (modal) modal.showModal();
    }

    /**
     * 处理删除映射
     */
    async handleDeleteMapping() {
        if (!this.currentMappingRule) return;

        const confirmBtn = document.getElementById('confirm-delete-mapping-btn');
        const spinner = confirmBtn?.querySelector('.loading');

        // 显示加载状态
        if (confirmBtn) confirmBtn.disabled = true;
        if (spinner) spinner.classList.remove('hidden');

        try {
            await api.deleteMappingRule(this.currentMappingType, this.currentMappingRule.id);
            toast.success('映射规则删除成功');

            // 关闭模态框并刷新数据
            const modal = document.getElementById('delete-mapping-modal');
            if (modal) modal.close();

            await this.loadMappingRules();

        } catch (error) {
            console.error('删除映射规则失败:', error);
            handleApiError(error, '删除映射规则');
        } finally {
            // 恢复按钮状态
            if (confirmBtn) confirmBtn.disabled = false;
            if (spinner) spinner.classList.add('hidden');
        }
    }

    /**
     * 显示批量删除确认模态框
     */
    showBatchDeleteModal() {
        if (this.selectedMappingIds.size === 0) {
            toast.warning('请先选择要删除的映射规则');
            return;
        }

        const modal = document.getElementById('batch-delete-mapping-modal');
        const message = document.getElementById('batch-delete-mapping-message');

        if (message) {
            const config = this.mappingTypeConfig[this.currentMappingType];
            message.textContent = `您确定要删除选中的 ${this.selectedMappingIds.size} 个${config ? config.name : ''}映射规则吗？`;
        }

        if (modal) modal.showModal();
    }

    /**
     * 处理批量删除映射
     */
    async handleBatchDeleteMapping() {
        if (this.selectedMappingIds.size === 0) return;

        const confirmBtn = document.getElementById('confirm-batch-delete-mapping-btn');
        const spinner = confirmBtn?.querySelector('.loading');

        // 显示加载状态
        if (confirmBtn) confirmBtn.disabled = true;
        if (spinner) spinner.classList.remove('hidden');

        try {
            // 使用批量删除API
            const idsArray = Array.from(this.selectedMappingIds);
            const result = await api.deleteMappingRules(this.currentMappingType, idsArray);

            if (result.success) {
                toast.success(`成功删除 ${this.selectedMappingIds.size} 个映射规则`);
            } else {
                // 处理部分成功的情况
                const data = result.data;
                if (data && data.success_count > 0) {
                    toast.warning(`删除完成：成功 ${data.success_count} 个，失败 ${data.failed_count} 个`);
                } else {
                    throw new Error(result.message || '删除失败');
                }
            }

            // 清空选择并关闭模态框
            this.selectedMappingIds.clear();
            const modal = document.getElementById('batch-delete-mapping-modal');
            if (modal) modal.close();

            await this.loadMappingRules();

        } catch (error) {
            console.error('批量删除映射规则失败:', error);
            handleApiError(error, '批量删除映射规则');
        } finally {
            // 恢复按钮状态
            if (confirmBtn) confirmBtn.disabled = false;
            if (spinner) spinner.classList.add('hidden');
        }
    }

    // ========== 导入导出功能 ==========

    /**
     * 显示导入模态框
     */
    showImportModal() {
        const modal = document.getElementById('import-mapping-modal');
        const fileInput = document.getElementById('mapping-file-input');
        const previewTextarea = document.getElementById('mapping-preview-textarea');
        const submitBtn = document.getElementById('import-mapping-submit-btn');

        // 重置表单
        if (fileInput) fileInput.value = '';
        if (previewTextarea) previewTextarea.value = '';
        if (submitBtn) submitBtn.disabled = true;

        if (modal) modal.showModal();
    }

    /**
     * 处理文件选择
     */
    async handleFileSelect(file) {
        const previewTextarea = document.getElementById('mapping-preview-textarea');
        const submitBtn = document.getElementById('import-mapping-submit-btn');

        if (!file) {
            if (previewTextarea) previewTextarea.value = '';
            if (submitBtn) submitBtn.disabled = true;
            return;
        }

        if (!file.name.toLowerCase().endsWith('.json')) {
            toast.error('请选择JSON格式的文件');
            return;
        }

        try {
            const text = await file.text();
            const data = JSON.parse(text);

            // 验证文件格式 - 支持多种格式
            let rules = [];
            if (Array.isArray(data.rules)) {
                // 标准格式：{ rules: [...] }
                rules = data.rules;
            } else if (Array.isArray(data)) {
                // 简单数组格式：[...]
                rules = data;
            } else {
                throw new Error('文件格式不正确，需要包含rules数组或直接为数组格式');
            }

            // 验证规则格式
            for (let i = 0; i < rules.length; i++) {
                const rule = rules[i];
                if (!rule.original && !rule.original_value) {
                    throw new Error(`第${i + 1}个规则缺少原始值字段(original或original_value)`);
                }
            }

            // 显示预览
            if (previewTextarea) {
                previewTextarea.value = JSON.stringify({ rules }, null, 2);
            }

            // 启用提交按钮
            if (submitBtn) submitBtn.disabled = false;

        } catch (error) {
            console.error('文件解析失败:', error);
            toast.error(`文件格式错误: ${error.message}`);

            if (previewTextarea) previewTextarea.value = '';
            if (submitBtn) submitBtn.disabled = true;
        }
    }

    /**
     * 处理导入提交
     */
    async handleImportSubmit() {
        const fileInput = document.getElementById('mapping-file-input');
        const overwriteCheckbox = document.getElementById('overwrite-mapping-checkbox');
        const submitBtn = document.getElementById('import-mapping-submit-btn');
        const spinner = submitBtn?.querySelector('.loading');

        if (!fileInput?.files[0]) {
            toast.error('请先选择文件');
            return;
        }

        // 显示加载状态
        if (submitBtn) submitBtn.disabled = true;
        if (spinner) spinner.classList.remove('hidden');

        try {
            const file = fileInput.files[0];
            const text = await file.text();
            const data = JSON.parse(text);

            // 处理不同的JSON格式
            let rules = [];
            if (Array.isArray(data.rules)) {
                rules = data.rules;
            } else if (Array.isArray(data)) {
                rules = data;
            } else {
                throw new Error('无效的文件格式');
            }

            const importData = {
                rules: rules.map(rule => ({
                    original_value: rule.original || rule.original_value,
                    mapped_value: rule.mapped || rule.mapped_value || null
                })),
                overwrite: overwriteCheckbox?.checked || false
            };

            const response = await api.importMappingRules(this.currentMappingType, importData);

            toast.success(`导入完成：成功 ${response.success_count} 个，失败 ${response.failed_count} 个`);

            // 关闭模态框并刷新数据
            const modal = document.getElementById('import-mapping-modal');
            if (modal) modal.close();

            await this.loadMappingRules();

        } catch (error) {
            console.error('导入映射规则失败:', error);
            handleApiError(error, '导入映射规则');
        } finally {
            // 恢复按钮状态
            if (submitBtn) submitBtn.disabled = false;
            if (spinner) spinner.classList.add('hidden');
        }
    }

    /**
     * 导出映射规则
     */
    async exportMappingRules() {
        try {
            const response = await api.exportMappingRules(this.currentMappingType);

            // 创建下载链接
            const blob = new Blob([JSON.stringify(response, null, 2)], {
                type: 'application/json'
            });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `${this.currentMappingType}_mappings_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            toast.success('映射规则导出成功');

        } catch (error) {
            console.error('导出映射规则失败:', error);
            handleApiError(error, '导出映射规则');
        }
    }

    // ========== 视频扩展名配置管理 ==========

    /**
     * 加载视频扩展名配置
     */
    async loadVideoExtensions() {
        if (this.isLoadingExtensions) return;

        try {
            this.isLoadingExtensions = true;

            const response = await window.api.getVideoExtensions();

            if (response.success && response.data) {
                this.currentVideoExtensions = response.data.extensions || [];
                this.defaultVideoExtensions = response.data.default_extensions || [];

                this.updateVideoExtensionsDisplay();

            } else {
                throw new Error(response.message || '获取视频扩展名配置失败');
            }

        } catch (error) {
            console.error('加载视频扩展名配置失败:', error);
            handleApiError(error, '加载视频扩展名配置');
        } finally {
            this.isLoadingExtensions = false;
        }
    }

    /**
     * 更新视频扩展名显示
     */
    updateVideoExtensionsDisplay() {
        const currentDisplay = document.getElementById('current-extensions-display');
        const extensionsCount = document.getElementById('extensions-count');

        // 更新当前扩展名显示
        if (currentDisplay) {
            currentDisplay.innerHTML = '';
            this.currentVideoExtensions.forEach(ext => {
                const badge = this.createExtensionBadge(ext, true);
                currentDisplay.appendChild(badge);
            });
        }

        // 更新扩展名数量
        if (extensionsCount) {
            extensionsCount.textContent = `${this.currentVideoExtensions.length} 个扩展名`;
        }
    }

    /**
     * 创建扩展名标签
     * @param {string} ext - 扩展名
     * @param {boolean} removable - 是否可删除
     * @returns {HTMLElement} 标签元素
     */
    createExtensionBadge(ext, removable = false) {
        const badge = document.createElement('div');
        badge.className = 'badge badge-primary badge-sm gap-2';

        const text = document.createElement('span');
        text.textContent = ext;
        badge.appendChild(text);

        if (removable) {
            const removeBtn = document.createElement('button');
            removeBtn.className = 'btn btn-ghost btn-xs p-0 min-h-0 h-4 w-4';
            removeBtn.innerHTML = '<i class="bi bi-x text-xs"></i>';
            removeBtn.onclick = (e) => {
                e.stopPropagation();
                this.removeVideoExtension(ext);
            };
            badge.appendChild(removeBtn);
        }

        return badge;
    }

    /**
     * 验证和格式化扩展名
     * @param {string} ext - 扩展名
     * @returns {string|null} 格式化后的扩展名，无效时返回null
     */
    validateAndFormatExtension(ext) {
        if (!ext || !ext.trim()) {
            return null;
        }

        const trimmed = ext.trim().toLowerCase();

        // 确保以点号开头
        const normalized = trimmed.startsWith('.') ? trimmed : '.' + trimmed;

        // 验证扩展名格式（只允许字母和数字）
        const extWithoutDot = normalized.substring(1);
        if (!/^[a-z0-9]+$/.test(extWithoutDot)) {
            return null;
        }

        return normalized;
    }

    /**
     * 添加视频扩展名
     * @param {string} ext - 扩展名
     */
    async addVideoExtension(ext) {
        const formatted = this.validateAndFormatExtension(ext);

        if (!formatted) {
            if (window.toast) {
                window.toast.error('扩展名格式无效，只允许字母和数字');
            }
            return;
        }

        if (this.currentVideoExtensions.includes(formatted)) {
            if (window.toast) {
                window.toast.warning('扩展名已存在');
            }
            return;
        }

        // 添加到当前列表
        this.currentVideoExtensions.push(formatted);
        this.currentVideoExtensions.sort();

        // 保存到服务器
        await this.saveVideoExtensions();
    }

    /**
     * 移除视频扩展名
     * @param {string} ext - 扩展名
     */
    async removeVideoExtension(ext) {
        const index = this.currentVideoExtensions.indexOf(ext);
        if (index === -1) return;

        // 从当前列表移除
        this.currentVideoExtensions.splice(index, 1);

        // 保存到服务器
        await this.saveVideoExtensions();
    }

    /**
     * 处理添加扩展名按钮点击
     */
    async handleAddExtension() {
        const input = document.getElementById('new-extension-input');
        if (!input) return;

        const ext = input.value.trim();
        if (!ext) return;

        await this.addVideoExtension(ext);

        // 清空输入框
        input.value = '';
    }

    /**
     * 保存视频扩展名配置到服务器
     */
    async saveVideoExtensions() {
        if (this.currentVideoExtensions.length === 0) {
            if (window.toast) {
                window.toast.error('扩展名列表不能为空');
            }
            return;
        }

        try {
            // 发送更新请求
            const response = await window.api.updateVideoExtensions(this.currentVideoExtensions);

            if (response.success && response.data) {
                this.currentVideoExtensions = response.data.extensions || [];
                this.updateVideoExtensionsDisplay();

                if (window.toast) {
                    window.toast.success('视频扩展名配置已保存');
                }

            } else {
                throw new Error(response.message || '保存视频扩展名配置失败');
            }

        } catch (error) {
            console.error('保存视频扩展名配置失败:', error);
            handleApiError(error, '保存视频扩展名配置');

            // 重新加载配置以恢复状态
            await this.loadVideoExtensions();
        }
    }

    // ========== 翻译配置相关方法 ==========

    /**
     * 绑定翻译配置相关事件
     */
    bindTranslationEvents() {
        // 翻译配置表单提交
        const translationForm = document.getElementById('translation-config-form');
        if (translationForm) {
            translationForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveTranslationConfig();
            });
        }

        // API Key 显示/隐藏切换
        const toggleApiKeyBtn = document.getElementById('toggle-api-key-visibility');
        const apiKeyInput = document.getElementById('translation-api-key');
        if (toggleApiKeyBtn && apiKeyInput) {
            toggleApiKeyBtn.addEventListener('click', () => {
                const isPassword = apiKeyInput.type === 'password';
                apiKeyInput.type = isPassword ? 'text' : 'password';
                const icon = toggleApiKeyBtn.querySelector('i');
                if (icon) {
                    icon.className = isPassword ? 'bi bi-eye-slash' : 'bi bi-eye';
                }
            });
        }

        // 测试翻译按钮
        const testTranslationBtn = document.getElementById('test-translation-btn');
        if (testTranslationBtn) {
            testTranslationBtn.addEventListener('click', () => {
                this.testTranslationConfig();
            });
        }

        // 重置配置按钮
        const resetConfigBtn = document.getElementById('reset-translation-config-btn');
        if (resetConfigBtn) {
            resetConfigBtn.addEventListener('click', () => {
                this.resetTranslationConfig();
            });
        }
    }

    /**
     * 加载翻译配置
     */
    async loadTranslationConfig() {
        try {
            this.isLoadingTranslationConfig = true;
            this.updateTranslationStatus('正在加载翻译配置...', 'info');

            const result = await window.api.getTranslationConfig();

            if (result.success && result.data) {
                this.translationConfig = result.data;
                this.updateTranslationForm();
                await this.checkTranslationStatus();
            } else {
                throw new Error(result.message || '加载翻译配置失败');
            }

        } catch (error) {
            console.error('加载翻译配置失败:', error);
            this.updateTranslationStatus('加载翻译配置失败', 'error');
        } finally {
            this.isLoadingTranslationConfig = false;
        }
    }

    /**
     * 更新翻译配置表单
     */
    updateTranslationForm() {
        const enabledCheckbox = document.getElementById('translation-enabled');
        const apiUrlInput = document.getElementById('translation-api-url');
        const apiKeyInput = document.getElementById('translation-api-key');
        const modelNameInput = document.getElementById('translation-model-name');
        const sourceLanguageSelect = document.getElementById('translation-source-language');
        const targetLanguageSelect = document.getElementById('translation-target-language');
        const promptTemplateTextarea = document.getElementById('translation-prompt-template');

        if (enabledCheckbox) enabledCheckbox.checked = this.translationConfig.enabled;
        if (apiUrlInput) apiUrlInput.value = this.translationConfig.api_url || '';

        // 检查 API Key 是否是加密显示的格式（包含星号），如果是则不填入输入框
        if (apiKeyInput) {
            const apiKey = this.translationConfig.api_key || '';
            if (apiKey && !apiKey.includes('*')) {
                // 只有当 API Key 不包含星号时才填入（即原始 API Key）
                apiKeyInput.value = apiKey;
            } else {
                // 如果是加密显示的 API Key，保持输入框为空，并添加占位符提示
                apiKeyInput.value = '';
                apiKeyInput.placeholder = apiKey ? '已配置 API Key，留空则不修改' : 'OpenAI: sk-... | OpenRouter: sk-or-...';
            }
        }

        if (modelNameInput) modelNameInput.value = this.translationConfig.model_name || 'gpt-3.5-turbo';
        if (sourceLanguageSelect) sourceLanguageSelect.value = this.translationConfig.source_language || '自动检测';
        if (targetLanguageSelect) targetLanguageSelect.value = this.translationConfig.target_language || '中文';
        if (promptTemplateTextarea) promptTemplateTextarea.value = this.translationConfig.prompt_template || '';
    }

    /**
     * 保存翻译配置
     */
    async saveTranslationConfig() {
        try {
            const saveBtn = document.getElementById('save-translation-config-btn');
            const spinner = saveBtn?.querySelector('.loading');

            if (saveBtn) saveBtn.disabled = true;
            if (spinner) spinner.classList.remove('hidden');

            // 收集表单数据
            const formData = this.collectTranslationFormData();

            // 验证必填字段
            if (formData.enabled) {
                if (!formData.api_url || !formData.model_name || !formData.source_language || !formData.target_language) {
                    throw new Error('启用翻译功能时，API URL、模型名称、源语言和目标语言为必填项');
                }

                // 检查 API Key：如果表单中没有 API Key 且当前配置也没有 API Key，则报错
                if (!formData.api_key && (!this.translationConfig || !this.translationConfig.api_key || this.translationConfig.api_key.includes('*'))) {
                    throw new Error('启用翻译功能时，API Key 为必填项');
                }
            }

            const result = await window.api.saveTranslationConfig(formData);

            if (result.success) {
                this.translationConfig = result.data;
                this.updateTranslationForm();
                await this.checkTranslationStatus();
                toast.success('翻译配置保存成功');
            } else {
                throw new Error(result.message || '保存翻译配置失败');
            }

        } catch (error) {
            console.error('保存翻译配置失败:', error);
            toast.error(error.message || '保存翻译配置失败');
        } finally {
            const saveBtn = document.getElementById('save-translation-config-btn');
            const spinner = saveBtn?.querySelector('.loading');

            if (saveBtn) saveBtn.disabled = false;
            if (spinner) spinner.classList.add('hidden');
        }
    }

    /**
     * 收集翻译配置表单数据
     */
    collectTranslationFormData() {
        const enabledCheckbox = document.getElementById('translation-enabled');
        const apiUrlInput = document.getElementById('translation-api-url');
        const apiKeyInput = document.getElementById('translation-api-key');
        const modelNameInput = document.getElementById('translation-model-name');
        const sourceLanguageSelect = document.getElementById('translation-source-language');
        const targetLanguageSelect = document.getElementById('translation-target-language');
        const promptTemplateTextarea = document.getElementById('translation-prompt-template');

        const formData = {
            enabled: enabledCheckbox?.checked || false,
            api_url: apiUrlInput?.value?.trim() || '',
            model_name: modelNameInput?.value?.trim() || 'gpt-3.5-turbo',
            source_language: sourceLanguageSelect?.value || '自动检测',
            target_language: targetLanguageSelect?.value || '中文',
            prompt_template: promptTemplateTextarea?.value?.trim() || ''
        };

        // 只有当 API Key 输入框有值时才包含在请求中
        const apiKeyValue = apiKeyInput?.value?.trim();
        if (apiKeyValue) {
            formData.api_key = apiKeyValue;
        }

        return formData;
    }

    /**
     * 测试翻译配置
     */
    async testTranslationConfig() {
        try {
            const testBtn = document.getElementById('test-translation-btn');
            const spinner = testBtn?.querySelector('.loading');

            if (testBtn) testBtn.disabled = true;
            if (spinner) spinner.classList.remove('hidden');

            this.updateTranslationStatus('正在测试翻译配置...', 'info');

            const result = await window.api.testTranslation();

            if (result.success) {
                this.updateTranslationStatus(
                    `翻译配置测试成功！测试文本: "${result.data.test_text}" → "${result.data.translated_text}"`,
                    'success'
                );
            } else {
                this.updateTranslationStatus(`翻译配置测试失败: ${result.message}`, 'error');
            }

        } catch (error) {
            console.error('测试翻译配置失败:', error);
            this.updateTranslationStatus('测试翻译配置失败', 'error');
        } finally {
            const testBtn = document.getElementById('test-translation-btn');
            const spinner = testBtn?.querySelector('.loading');

            if (testBtn) testBtn.disabled = false;
            if (spinner) spinner.classList.add('hidden');
        }
    }

    /**
     * 重置翻译配置为默认值
     */
    resetTranslationConfig() {
        const defaultConfig = {
            enabled: false,
            api_url: 'https://api.openai.com/v1/chat/completions',
            api_key: '',
            model_name: 'gpt-3.5-turbo',
            source_language: '自动检测',
            target_language: '中文',
            prompt_template: '请将以下{source_language}文本翻译成{target_language}，保持原意不变，只返回翻译结果：\n\n{text}'
        };

        this.translationConfig = defaultConfig;
        this.updateTranslationForm();
        this.updateTranslationStatus('配置已重置为默认值', 'info');
    }

    /**
     * 检查翻译服务状态
     */
    async checkTranslationStatus() {
        try {
            const result = await window.api.getTranslationStatus();

            if (result.success && result.data) {
                const status = result.data;
                let message = '';
                let type = 'info';

                if (!status.enabled) {
                    message = '翻译功能已禁用';
                    type = 'warning';
                } else if (!status.configured) {
                    message = '翻译功能已启用，但配置不完整';
                    type = 'error';
                } else {
                    message = '翻译功能已启用且配置完整';
                    type = 'success';
                }

                this.updateTranslationStatus(message, type);
            }

        } catch (error) {
            console.error('检查翻译服务状态失败:', error);
            this.updateTranslationStatus('无法获取翻译服务状态', 'error');
        }
    }

    /**
     * 更新翻译状态显示
     */
    updateTranslationStatus(message, type = 'info') {
        const statusElement = document.getElementById('translation-status');
        if (!statusElement) return;

        // 移除所有状态类
        statusElement.classList.remove('alert-info', 'alert-success', 'alert-warning', 'alert-error');

        // 添加对应的状态类
        switch (type) {
            case 'success':
                statusElement.classList.add('alert-success');
                break;
            case 'warning':
                statusElement.classList.add('alert-warning');
                break;
            case 'error':
                statusElement.classList.add('alert-error');
                break;
            default:
                statusElement.classList.add('alert-info');
        }

        // 更新图标和消息
        const icon = statusElement.querySelector('i');
        const span = statusElement.querySelector('span');

        if (icon) {
            icon.className = type === 'success' ? 'bi bi-check-circle' :
                           type === 'warning' ? 'bi bi-exclamation-triangle' :
                           type === 'error' ? 'bi bi-x-circle' : 'bi bi-info-circle';
        }

        if (span) {
            span.textContent = message;
        }
    }

    // ========== 映射翻译功能相关方法 ==========

    /**
     * 绑定映射翻译功能事件
     */
    async bindMappingTranslationEvents() {
        // 检查翻译功能是否启用
        await this.checkAndUpdateMappingTranslationButtons();

        // 单个字段翻译按钮
        document.querySelectorAll('#mapping-modal .translation-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleMappingTranslation(btn);
            });
        });
    }

    /**
     * 检查翻译功能状态并更新映射翻译按钮显示
     */
    async checkAndUpdateMappingTranslationButtons() {
        try {
            // 检查翻译服务状态
            const result = await window.api.getTranslationStatus();
            const isEnabled = result.success && result.data && result.data.enabled;

            // 获取映射模态框中的翻译按钮
            const translationButtons = document.querySelectorAll('#mapping-modal .translation-btn');

            // 根据启用状态显示或隐藏按钮
            translationButtons.forEach(btn => {
                if (isEnabled) {
                    btn.style.display = '';
                    btn.classList.remove('hidden');
                } else {
                    btn.style.display = 'none';
                    btn.classList.add('hidden');
                }
            });

        } catch (error) {
            console.error('检查映射翻译状态失败:', error);
            // 出错时隐藏翻译按钮
            document.querySelectorAll('#mapping-modal .translation-btn').forEach(btn => {
                btn.style.display = 'none';
                btn.classList.add('hidden');
            });
        }
    }

    /**
     * 处理映射翻译
     */
    async handleMappingTranslation(button) {
        try {
            // 获取原始值输入框的内容
            const originalValueInput = document.getElementById('mapping-original-value');
            const mappedValueInput = document.getElementById('mapping-mapped-value');

            if (!originalValueInput || !mappedValueInput) {
                toast.error('找不到输入框元素');
                return;
            }

            const originalValue = originalValueInput.value.trim();
            if (!originalValue) {
                toast.warning('请先输入原始值');
                return;
            }

            // 设置按钮加载状态
            this.setMappingTranslationButtonLoading(button, true);

            // 调用翻译API
            const result = await window.translationHelper.translateText(originalValue, 'mapped_value');

            if (result.success && result.translatedText) {
                // 将翻译结果填入映射值输入框
                mappedValueInput.value = result.translatedText;
                toast.success('翻译完成');

                // 触发输入事件以进行验证
                mappedValueInput.dispatchEvent(new Event('input', { bubbles: true }));
            } else {
                throw new Error(result.error || '翻译失败');
            }

        } catch (error) {
            console.error('映射翻译失败:', error);
            toast.error(`翻译失败: ${error.message}`);
        } finally {
            // 恢复按钮状态
            this.setMappingTranslationButtonLoading(button, false);
        }
    }

    /**
     * 设置映射翻译按钮加载状态
     */
    setMappingTranslationButtonLoading(button, isLoading) {
        if (!button) return;

        const spinner = button.querySelector('.loading');
        const icon = button.querySelector('.bi-translate');

        if (isLoading) {
            button.disabled = true;
            if (spinner) spinner.classList.remove('hidden');
            if (icon) icon.classList.add('hidden');
        } else {
            button.disabled = false;
            if (spinner) spinner.classList.add('hidden');
            if (icon) icon.classList.remove('hidden');
        }
    }


}

// 页面加载完成后初始化设置管理器
document.addEventListener('DOMContentLoaded', async () => {
    if (window.location.pathname === '/settings') {
        // 确保API客户端已加载
        if (typeof window.api === 'undefined') {
            setTimeout(() => {
                if (typeof window.api !== 'undefined') {
                    window.settingsManager = new SettingsManager();
                    window.settingsManager.init();
                }
            }, 100);
        } else {
            window.settingsManager = new SettingsManager();
            await window.settingsManager.init();
        }
    }
});
