"""
影片管理 API 路由
提供影片的查询、更新、删除等操作接口
"""
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import Optional
from pathlib import Path
from app.core.database import get_db
from app.models.models import Movie, Directory
from app.services.movie_service import MovieService
from app.services.nfo_sync_service import NFOSyncService
from app.services.nfo_sync_progress_service import nfo_sync_progress_service
from app.schemas.schemas import (
    MovieUpdate, MovieResponse, MovieListResponse, MovieListItem,
    MovieFilterParams, BaseResponse, TagResponse, GenreResponse,
    SeriesResponse, ActorResponse, BatchAssociationRequest,
    MovieDeleteRequest, BatchDeleteResponse
)
import logging

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(
    prefix="/movies",
    tags=["影片库"],
    responses={404: {"description": "影片不存在"}}
)


@router.post("/list", response_model=MovieListResponse)
async def get_movies(
    filter_params: MovieFilterParams,
    db: Session = Depends(get_db)
):
    """
    获取影片列表

    支持多种过滤和搜索条件：
    - 按标题、原始标题、剧情、演员名称搜索
    - 按分类、系列、标签多选过滤
    - 按年份、评分范围过滤
    - 按目录ID过滤
    - 分页显示和排序

    Args:
        filter_params: 过滤参数（JSON格式）

    Returns:
        影片列表，包含基本信息和关联数据统计
    """
    try:

        movie_service = MovieService(db)
        movies, total_count = movie_service.get_movies(filter_params)
        
        # 构建响应数据
        movie_items = []

        # 批量获取所有影片的图片UUID，提高性能
        movies_image_uuids = movie_service.get_movies_with_image_uuids(movies)

        # 批量获取所有影片的收藏状态，提高性能
        movie_ids = [movie.id for movie in movies]
        movies_favorite_status = movie_service.get_movies_favorite_status(movie_ids)

        for movie in movies:
            # 获取该影片的图片UUID
            image_uuids = movies_image_uuids.get(movie.id, {})

            # 获取该影片的收藏状态
            is_favorited = movies_favorite_status.get(movie.id, False)

            # 构建列表项
            item = MovieListItem(
                id=movie.id,
                title=movie.title,
                year=movie.year,
                rating=movie.rating,
                runtime=movie.runtime,
                poster_uuid=image_uuids.get("poster_uuid"),
                fanart_uuid=image_uuids.get("fanart_uuid"),
                thumb_uuid=image_uuids.get("thumb_uuid"),
                series_name=movie.series.name if movie.series else None,
                genre_count=len(movie.genres),
                tag_count=len(movie.tags),
                actor_count=len(movie.actors),
                is_favorited=is_favorited,
                is_updated=movie.is_updated,  # 添加缺失的字段
                movie_updated_at=movie.updated_at  # 添加缺失的字段
            )
            movie_items.append(item)
        
        return MovieListResponse(
            success=True,
            message=f"获取到 {len(movies)} 部影片",
            data=movie_items,
            total_count=total_count,
            limit=filter_params.limit,
            offset=filter_params.offset
        )
        
    except Exception as e:
        logger.error(f"获取影片列表时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取影片列表失败: {str(e)}")


@router.get("/info/{movie_id}", response_model=BaseResponse)
async def get_movie_by_id(movie_id: int, db: Session = Depends(get_db)):
    """
    通过ID获取指定影片的完整详情

    Args:
        movie_id: 影片ID

    Returns:
        影片完整信息，包括关联的演员、分类、系列、标签等
    """
    try:
        movie_service = MovieService(db)
        movie = movie_service.get_movie_by_id(movie_id, check_directory_enabled=True)

        if not movie:
            # 尝试获取影片但不检查目录状态，以确定是影片不存在还是目录被停用
            movie_without_check = movie_service.get_movie_by_id(movie_id, check_directory_enabled=False)
            if movie_without_check:
                raise HTTPException(status_code=403, detail="该影片所在目录已被停用，无法访问")
            else:
                raise HTTPException(status_code=404, detail="影片不存在")
        
        # 获取图片UUID
        movie_info = movie_service.get_movie_with_image_uuids(movie)
        
        # 构建响应数据
        movie_response = MovieResponse(
            id=movie.id,
            title=movie.title,
            original_title=movie.original_title,
            year=movie.year,
            rating=movie.rating,
            runtime=movie.runtime,
            plot=movie.plot,
            outline=movie.outline,
            country=movie.country,
            critic_rating=movie.critic_rating,
            sort_title=movie.sort_title,
            trailer=movie.trailer,
            num=movie.num,
            file_path=movie.file_path,
            poster_uuid=movie_info["poster_uuid"],
            fanart_uuid=movie_info["fanart_uuid"],
            thumb_uuid=movie_info["thumb_uuid"],
            created_at=movie.created_at,
            updated_at=movie.updated_at,
            is_updated=movie.is_updated,  # 添加缺失的字段
            movie_updated_at=movie.updated_at,  # 添加缺失的字段
            series=SeriesResponse.model_validate(movie.series) if movie.series else None,
            tags=[TagResponse.model_validate(tag) for tag in movie.tags],
            genres=[GenreResponse.model_validate(genre) for genre in movie.genres],
            actors=[ActorResponse.model_validate(actor) for actor in movie.actors]
        )
        
        return BaseResponse(
            success=True,
            message="获取影片详情成功",
            data=movie_response
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取影片详情时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取影片详情失败: {str(e)}")



@router.put("/edit/{movie_id}", response_model=BaseResponse)
async def update_movie(movie_id: int, update_data: MovieUpdate, db: Session = Depends(get_db)):
    """
    更新影片信息
    
    Args:
        movie_id: 影片ID
        update_data: 更新数据
        
    Returns:
        更新结果
        
    Note:
        - 支持更新基本信息：标题、年份、评分、剧情等
        - 支持更新关联关系：演员、分类、系列、标签
        - 文件路径等系统信息不可更改
    """
    try:
        movie_service = MovieService(db)
        movie = movie_service.update_movie(movie_id, update_data)
        
        if not movie:
            raise HTTPException(status_code=404, detail="影片不存在或更新失败")
        
        # 获取更新后的完整信息
        updated_movie = movie_service.get_movie_by_id(movie_id)
        movie_info = movie_service.get_movie_with_image_uuids(updated_movie)
        
        movie_response = MovieResponse(
            id=updated_movie.id,
            title=updated_movie.title,
            original_title=updated_movie.original_title,
            year=updated_movie.year,
            rating=updated_movie.rating,
            runtime=updated_movie.runtime,
            plot=updated_movie.plot,
            outline=updated_movie.outline,
            country=updated_movie.country,
            critic_rating=updated_movie.critic_rating,
            sort_title=updated_movie.sort_title,
            trailer=updated_movie.trailer,
            num=updated_movie.num,
            file_path=updated_movie.file_path,
            poster_uuid=movie_info["poster_uuid"],
            fanart_uuid=movie_info["fanart_uuid"],
            thumb_uuid=movie_info["thumb_uuid"],
            created_at=updated_movie.created_at,
            updated_at=updated_movie.updated_at,
            is_updated=updated_movie.is_updated,  # 添加缺失的字段
            movie_updated_at=updated_movie.updated_at,  # 添加缺失的字段
            series=SeriesResponse.model_validate(updated_movie.series) if updated_movie.series else None,
            tags=[TagResponse.model_validate(tag) for tag in updated_movie.tags],
            genres=[GenreResponse.model_validate(genre) for genre in updated_movie.genres],
            actors=[ActorResponse.model_validate(actor) for actor in updated_movie.actors]
        )
        
        return BaseResponse(
            success=True,
            message="影片更新成功",
            data=movie_response
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新影片时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"更新影片失败: {str(e)}")



@router.delete("", response_model=BatchDeleteResponse)
async def delete_movies(
    request: MovieDeleteRequest,
    db: Session = Depends(get_db)
):
    """
    批量删除影片记录

    Args:
        request: 批量删除请求，包含影片ID列表和强制删除标志

    Returns:
        批量删除结果

    Note:
        - 仅删除数据库中的影片记录和关联关系
        - 不删除物理文件（视频文件、图片文件、NFO文件等）
        - 会清理相关的虚拟图片映射记录
    """
    try:
        movie_service = MovieService(db)
        result = movie_service.batch_delete_movies(request)

        # 构建响应消息
        if result.failed_count == 0:
            message = f"成功删除 {result.success_count} 个影片"
        elif result.success_count == 0:
            message = f"删除失败，{result.failed_count} 个影片无法删除"
        else:
            message = f"成功删除 {result.success_count} 个影片，{result.failed_count} 个失败"

        return BatchDeleteResponse(
            success=True,
            message=message,
            data=result
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量删除影片时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"批量删除影片失败: {str(e)}")




@router.post("/sync-nfo/{movie_id}", response_model=BaseResponse)
async def sync_movie_to_nfo(movie_id: int, db: Session = Depends(get_db)):
    """
    将数据库中的影片信息同步到NFO文件

    Args:
        movie_id: 影片ID

    Returns:
        同步结果，包含更新的字段信息
    """
    try:
        # 使用重构后的NFO同步服务
        nfo_sync_service = NFOSyncService(db)
        sync_result = nfo_sync_service.sync_single_movie(movie_id)

        if sync_result['success']:
            return BaseResponse(
                success=True,
                message=sync_result['message'],
                data={
                    'updated_fields': sync_result['updated_fields'],
                    'nfo_path': sync_result.get('nfo_path'),
                    'movie_title': sync_result.get('movie_title', '影片')
                }
            )
        else:
            raise HTTPException(status_code=400, detail=sync_result['message'])

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"同步NFO文件时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"同步NFO文件失败: {str(e)}")


@router.get("/nfo-status/{movie_id}", response_model=BaseResponse)
async def get_nfo_sync_status(movie_id: int, db: Session = Depends(get_db)):
    """
    获取影片的NFO同步状态

    Args:
        movie_id: 影片ID

    Returns:
        NFO文件状态信息
    """
    try:
        movie_service = MovieService(db)
        movie = movie_service.get_movie_by_id(movie_id)

        if not movie:
            raise HTTPException(status_code=404, detail="影片不存在")

        # 检查NFO文件状态
        nfo_path = NFOSyncService._get_nfo_path(movie)

        if not nfo_path:
            return BaseResponse(
                success=True,
                message="无法确定NFO文件路径",
                data={
                    'has_nfo': False,
                    'nfo_path': None,
                    'can_sync': False,
                    'reason': '无法确定NFO文件路径'
                }
            )

        # 检查文件权限
        permission_check = NFOSyncService._check_file_permissions(nfo_path)

        return BaseResponse(
            success=True,
            message="NFO状态检查完成",
            data={
                'has_nfo': Path(nfo_path).exists(),
                'nfo_path': nfo_path,
                'can_sync': permission_check['success'],
                'reason': permission_check.get('message', '可以同步') if not permission_check['success'] else '可以同步'
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"检查NFO状态时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"检查NFO状态失败: {str(e)}")


@router.post("/batch/add-associations", response_model=BaseResponse)
async def batch_add_associations(
    request: BatchAssociationRequest, 
    db: Session = Depends(get_db)
):
    """批量为影片添加标签、分类或系列"""
    try:
        movie_service = MovieService(db)
        movie_service.batch_add_associations(request)
        return BaseResponse(success=True, message="批量添加关联成功")
    except Exception as e:
        logger.error(f"批量添加关联时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"批量添加关联失败: {str(e)}")


@router.post("/batch/remove-associations", response_model=BaseResponse)
async def batch_remove_associations(
    request: BatchAssociationRequest,
    db: Session = Depends(get_db)
):
    """批量为影片移除标签、分类或系列"""
    try:
        movie_service = MovieService(db)
        movie_service.batch_remove_associations(request)
        return BaseResponse(success=True, message="批量移除关联成功")
    except Exception as e:
        logger.error(f"批量移除关联时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"批量移除关联失败: {str(e)}")


@router.post("/batch/sync-nfo", response_model=BaseResponse)
async def batch_sync_nfo(
    request: BatchAssociationRequest,
    db: Session = Depends(get_db)
):
    """
    批量同步影片信息到NFO文件（带进度追踪）

    Args:
        request: 批量操作请求，只使用movie_ids字段

    Returns:
        任务ID，用于查询进度
    """
    try:
        # 参数验证
        if not request.movie_ids:
            raise HTTPException(status_code=400, detail="影片ID列表不能为空")

        if len(request.movie_ids) > 100:
            raise HTTPException(status_code=400, detail="单次最多只能同步100个影片")

        # 使用带进度追踪的NFO同步服务
        nfo_sync_service = NFOSyncService(db)
        task_id = nfo_sync_service.sync_batch_movies_with_progress(request.movie_ids)

        return BaseResponse(
            success=True,
            message="NFO同步任务已启动",
            data={
                'task_id': task_id,
                'total_count': len(request.movie_ids)
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动批量NFO同步任务时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"启动批量NFO同步任务失败: {str(e)}")


@router.post("/batch/sync-updated-nfo", response_model=BaseResponse)
async def batch_sync_updated_nfo(
    db: Session = Depends(get_db)
):
    """
    批量同步所有已更新影片的NFO文件（带进度追踪）

    获取所有is_updated=true的影片并执行批量NFO同步

    Returns:
        任务ID，用于查询进度
    """
    try:
        # 使用带进度追踪的NFO同步服务
        nfo_sync_service = NFOSyncService(db)
        task_id = nfo_sync_service.sync_updated_movies_with_progress()

        return BaseResponse(
            success=True,
            message="NFO同步任务已启动",
            data={
                'task_id': task_id
            }
        )

    except Exception as e:
        logger.error(f"启动批量NFO同步任务时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"启动批量NFO同步任务失败: {str(e)}")


@router.get("/nfo-sync/progress/{task_id}", response_model=BaseResponse)
async def get_nfo_sync_progress(task_id: str):
    """
    获取NFO同步任务进度

    Args:
        task_id: 任务ID

    Returns:
        任务进度信息
    """
    try:
        progress = nfo_sync_progress_service.get_sync_progress(task_id)

        if not progress:
            raise HTTPException(status_code=404, detail="任务不存在")

        return BaseResponse(
            success=True,
            message="获取NFO同步进度成功",
            data=progress
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取NFO同步进度时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取NFO同步进度失败: {str(e)}")


@router.post("/nfo-sync/cancel/{task_id}", response_model=BaseResponse)
async def cancel_nfo_sync(task_id: str):
    """
    取消NFO同步任务

    Args:
        task_id: 任务ID

    Returns:
        取消结果
    """
    try:
        success = nfo_sync_progress_service.cancel_sync_task(task_id)

        if not success:
            raise HTTPException(status_code=404, detail="任务不存在或无法取消")

        return BaseResponse(
            success=True,
            message="NFO同步任务已取消"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消NFO同步任务时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"取消NFO同步任务失败: {str(e)}")

