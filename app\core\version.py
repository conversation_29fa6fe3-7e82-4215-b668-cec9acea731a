"""
版本管理模块
提供统一的版本号管理
"""
import os
import toml
from pathlib import Path

def get_app_version() -> str:
    """
    获取应用版本号
    
    Returns:
        str: 版本号字符串
    """
    try:
        # 获取项目根目录
        project_root = Path(__file__).parent.parent.parent
        pyproject_path = project_root / "pyproject.toml"
        
        if pyproject_path.exists():
            # 从 pyproject.toml 读取版本号
            with open(pyproject_path, 'r', encoding='utf-8') as f:
                pyproject_data = toml.load(f)
                return pyproject_data.get('project', {}).get('version', '0.9.49')
        else:
            # 如果文件不存在，返回默认版本号
            return '0.0.0'
            
    except Exception as e:
        # 出错时返回默认版本号
        print(f"获取版本号失败: {e}")
        return '0.0.0'

# 全局版本号变量
APP_VERSION = get_app_version()
