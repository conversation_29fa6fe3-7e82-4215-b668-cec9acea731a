"""
Pydantic 模型定义
用于 API 请求验证和响应序列化
"""
from datetime import datetime
from typing import Optional, List, Any, Union, Dict
from pydantic import BaseModel, Field, field_validator


# 基础响应模型
class BaseResponse(BaseModel):
    """基础响应模型"""
    success: bool
    message: str
    data: Optional[Any] = None


class PaginationParams(BaseModel):
    """分页参数模型"""
    limit: int = Field(default=50, ge=1, le=100, description="每页数量，1-100之间")
    offset: int = Field(default=0, ge=0, description="偏移量，从0开始")
    search: Optional[str] = Field(default=None, description="搜索关键词")


# 标签相关模型
class TagCreate(BaseModel):
    """创建标签请求模型"""
    name: str = Field(..., min_length=1, max_length=100, description="标签名称")
    description: Optional[str] = Field(default=None, max_length=500, description="标签描述")
    
    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        if not v or not v.strip():
            raise ValueError('标签名称不能为空')
        return v.strip()


class TagUpdate(BaseModel):
    """更新标签请求模型"""
    name: Optional[str] = Field(default=None, min_length=1, max_length=100, description="标签名称")
    description: Optional[str] = Field(default=None, max_length=500, description="标签描述")
    
    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        if v is not None and (not v or not v.strip()):
            raise ValueError('标签名称不能为空')
        return v.strip() if v else v


class TagResponse(BaseModel):
    """标签响应模型"""
    id: int
    name: str
    description: Optional[str]
    created_at: datetime
    updated_at: datetime
    movie_count: Optional[int] = None  # 关联的电影数量

    model_config = {"from_attributes": True}


class TagListRequest(BaseModel):
    """标签列表请求模型"""
    limit: int = Field(default=50, ge=1, le=1000, description="每页数量")
    offset: int = Field(default=0, ge=0, description="偏移量")
    search: Optional[str] = Field(default=None, description="搜索关键词")


class TagListResponse(BaseResponse):
    """标签列表响应模型"""
    data: Optional[List[TagResponse]] = None
    total_count: Optional[int] = None
    limit: Optional[int] = None
    offset: Optional[int] = None


# 分类相关模型
class GenreCreate(BaseModel):
    """创建分类请求模型"""
    name: str = Field(..., min_length=1, max_length=100, description="分类名称")
    description: Optional[str] = Field(default=None, max_length=500, description="分类描述")
    
    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        if not v or not v.strip():
            raise ValueError('分类名称不能为空')
        return v.strip()


class GenreUpdate(BaseModel):
    """更新分类请求模型"""
    name: Optional[str] = Field(default=None, min_length=1, max_length=100, description="分类名称")
    description: Optional[str] = Field(default=None, max_length=500, description="分类描述")
    
    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        if v is not None and (not v or not v.strip()):
            raise ValueError('分类名称不能为空')
        return v.strip() if v else v


class GenreResponse(BaseModel):
    """分类响应模型"""
    id: int
    name: str
    description: Optional[str]
    created_at: datetime
    updated_at: datetime
    movie_count: Optional[int] = None  # 关联的电影数量

    model_config = {"from_attributes": True}


class GenreListRequest(BaseModel):
    """分类列表请求模型"""
    limit: int = Field(default=50, ge=1, le=1000, description="每页数量")
    offset: int = Field(default=0, ge=0, description="偏移量")
    search: Optional[str] = Field(default=None, description="搜索关键词")


class GenreListResponse(BaseResponse):
    """分类列表响应模型"""
    data: Optional[List[GenreResponse]] = None
    total_count: Optional[int] = None
    limit: Optional[int] = None
    offset: Optional[int] = None


# 系列相关模型
class SeriesCreate(BaseModel):
    """创建系列请求模型"""
    name: str = Field(..., min_length=1, max_length=255, description="系列名称")
    description: Optional[str] = Field(default=None, max_length=1000, description="系列描述")
    
    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        if not v or not v.strip():
            raise ValueError('系列名称不能为空')
        return v.strip()


class SeriesUpdate(BaseModel):
    """更新系列请求模型"""
    name: Optional[str] = Field(default=None, min_length=1, max_length=255, description="系列名称")
    description: Optional[str] = Field(default=None, max_length=1000, description="系列描述")
    
    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        if v is not None and (not v or not v.strip()):
            raise ValueError('系列名称不能为空')
        return v.strip() if v else v


class SeriesResponse(BaseModel):
    """系列响应模型"""
    id: int
    name: str
    description: Optional[str]
    created_at: datetime
    updated_at: datetime
    movie_count: Optional[int] = None  # 关联的电影数量

    model_config = {"from_attributes": True}


class SeriesListRequest(BaseModel):
    """系列列表请求模型"""
    limit: int = Field(default=50, ge=1, le=1000, description="每页数量")
    offset: int = Field(default=0, ge=0, description="偏移量")
    search: Optional[str] = Field(default=None, description="搜索关键词")


class SeriesListResponse(BaseResponse):
    """系列列表响应模型"""
    data: Optional[List[SeriesResponse]] = None
    total_count: Optional[int] = None
    limit: Optional[int] = None
    offset: Optional[int] = None


# 删除操作参数模型
class DeleteParams(BaseModel):
    """删除操作参数模型"""
    force: bool = Field(default=False, description="是否强制删除（忽略关联关系）")


# 统一删除相关模型
class UnifiedDeleteRequest(BaseModel):
    """统一删除请求模型（支持单项和批量删除）"""
    ids: Union[int, List[int]] = Field(..., description="要删除的ID（单个数字）或ID列表（数组）")
    force: bool = Field(default=False, description="是否强制删除（忽略关联关系）")

    @field_validator('ids')
    @classmethod
    def validate_ids(cls, v):
        if isinstance(v, int):
            if v <= 0:
                raise ValueError('ID必须为正整数')
            return [v]  # 转换为列表格式统一处理
        elif isinstance(v, list):
            if not v:
                raise ValueError('ID列表不能为空')
            # 验证每个ID都是正整数
            for id_val in v:
                if not isinstance(id_val, int) or id_val <= 0:
                    raise ValueError('所有ID必须为正整数')
            # 去重并保持顺序
            seen = set()
            unique_ids = []
            for id_val in v:
                if id_val not in seen:
                    seen.add(id_val)
                    unique_ids.append(id_val)
            return unique_ids
        else:
            raise ValueError('ids必须是整数或整数列表')


# 保持向后兼容的批量删除模型
class BatchDeleteRequest(BaseModel):
    """批量删除请求模型（向后兼容）"""
    ids: List[int] = Field(..., min_length=1, description="要删除的ID列表")
    force: bool = Field(default=False, description="是否强制删除（忽略关联关系）")

    @field_validator('ids')
    @classmethod
    def validate_ids(cls, v):
        if not v:
            raise ValueError('ID列表不能为空')
        # 去重并保持顺序
        seen = set()
        unique_ids = []
        for id_val in v:
            if id_val not in seen:
                seen.add(id_val)
                unique_ids.append(id_val)
        return unique_ids


class BatchDeleteFailedItem(BaseModel):
    """批量删除失败项模型"""
    id: int = Field(..., description="失败的ID")
    error: str = Field(..., description="失败原因")


class BatchDeleteResult(BaseModel):
    """批量删除结果模型"""
    total_count: int = Field(..., description="总数量")
    success_count: int = Field(..., description="成功数量")
    failed_count: int = Field(..., description="失败数量")
    success_ids: List[int] = Field(default_factory=list, description="成功删除的ID列表")
    failed_items: List[BatchDeleteFailedItem] = Field(default_factory=list, description="失败项列表")


class BatchDeleteResponse(BaseResponse):
    """批量删除响应模型"""
    data: Optional[BatchDeleteResult] = None


# 统计信息模型
class EntityStats(BaseModel):
    """实体统计信息模型"""
    total_count: int
    used_count: int  # 被电影使用的数量
    unused_count: int  # 未被使用的数量


# 演员相关模型
class ActorCreate(BaseModel):
    """创建演员请求模型"""
    name: str = Field(..., min_length=1, max_length=255, description="演员姓名")
    role: Optional[str] = Field(default=None, max_length=255, description="角色名称")
    biography: Optional[str] = Field(default=None, max_length=1000, description="演员简介")
    actor_type: Optional[str] = Field(default="Actor", max_length=50, description="演员类型")

    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        if not v or not v.strip():
            raise ValueError('演员姓名不能为空')
        return v.strip()


class ActorUpdate(BaseModel):
    """更新演员请求模型"""
    name: Optional[str] = Field(default=None, min_length=1, max_length=255, description="演员姓名")
    role: Optional[str] = Field(default=None, max_length=255, description="角色名称")
    biography: Optional[str] = Field(default=None, max_length=1000, description="演员简介")
    actor_type: Optional[str] = Field(default=None, max_length=50, description="演员类型")

    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        if v is not None and (not v or not v.strip()):
            raise ValueError('演员姓名不能为空')
        return v.strip() if v else v


class ActorResponse(BaseModel):
    """演员响应模型"""
    id: int
    name: str
    role: Optional[str]
    actor_type: Optional[str]
    biography: Optional[str]
    created_at: datetime
    updated_at: datetime
    movie_count: Optional[int] = None  # 关联的电影数量

    model_config = {"from_attributes": True}


class ActorListRequest(BaseModel):
    """演员列表请求模型"""
    limit: int = Field(default=50, ge=1, le=1000, description="每页数量")
    offset: int = Field(default=0, ge=0, description="偏移量")
    search: Optional[str] = Field(default=None, description="搜索关键词")


class ActorListResponse(BaseResponse):
    """演员列表响应模型"""
    data: Optional[List[ActorResponse]] = None
    total_count: Optional[int] = None
    limit: Optional[int] = None
    offset: Optional[int] = None


# 影片相关模型


class MovieUpdate(BaseModel):
    """更新影片请求模型"""
    title: Optional[str] = Field(default=None, min_length=1, max_length=255, description="影片标题")
    original_title: Optional[str] = Field(default=None, max_length=255, description="原始标题")
    year: Optional[int] = Field(default=None, ge=1900, le=2100, description="年份")
    rating: Optional[float] = Field(default=None, ge=0, le=10, description="评分")
    runtime: Optional[int] = Field(default=None, ge=0, description="时长（分钟）")
    plot: Optional[str] = Field(default=None, description="剧情简介")
    outline: Optional[str] = Field(default=None, description="简短描述")
    country: Optional[str] = Field(default=None, max_length=100, description="国家")
    critic_rating: Optional[float] = Field(default=None, ge=0, le=100, description="影评人评分")
    sort_title: Optional[str] = Field(default=None, max_length=255, description="排序标题")
    trailer: Optional[str] = Field(default=None, max_length=500, description="预告片链接")
    num: Optional[str] = Field(default=None, max_length=50, description="编号")
    series_id: Optional[int] = Field(default=None, description="系列ID")
    tag_ids: Optional[List[int]] = Field(default=None, description="标签ID列表")
    genre_ids: Optional[List[int]] = Field(default=None, description="分类ID列表")
    actor_ids: Optional[List[int]] = Field(default=None, description="演员ID列表")

    @field_validator('title')
    @classmethod
    def validate_title(cls, v):
        if v is not None and (not v or not v.strip()):
            raise ValueError('影片标题不能为空')
        return v.strip() if v else v


class MovieResponse(BaseModel):
    """影片响应模型"""
    id: int
    title: str
    original_title: Optional[str]
    year: Optional[int]
    rating: Optional[float]
    runtime: Optional[int]
    plot: Optional[str]
    outline: Optional[str]
    country: Optional[str]
    critic_rating: Optional[float]
    sort_title: Optional[str]
    trailer: Optional[str]
    num: Optional[str]
    file_path: str
    poster_uuid: Optional[str] = None  # 虚拟海报 UUID
    fanart_uuid: Optional[str] = None  # 虚拟剧照 UUID
    thumb_uuid: Optional[str] = None   # 虚拟缩略图 UUID
    created_at: datetime
    updated_at: Optional[datetime] = None  # 记录更新时间，可能为空
    is_updated: Optional[bool] = False  # 是否已更新标记
    movie_updated_at: Optional[datetime] = None  # 影片最后更新时间

    # 关联数据
    series: Optional[SeriesResponse] = None
    tags: Optional[List[TagResponse]] = None
    genres: Optional[List[GenreResponse]] = None
    actors: Optional[List[ActorResponse]] = None

    model_config = {"from_attributes": True}


class MovieListItem(BaseModel):
    """影片列表项模型（简化版）"""
    id: int
    title: str
    year: Optional[int]
    rating: Optional[float]
    runtime: Optional[int]
    poster_uuid: Optional[str] = None    # 虚拟海报 UUID
    fanart_uuid: Optional[str] = None    # 虚拟剧照 UUID
    thumb_uuid: Optional[str] = None     # 虚拟缩略图 UUID
    series_name: Optional[str] = None
    genre_count: Optional[int] = 0
    tag_count: Optional[int] = 0
    actor_count: Optional[int] = 0
    is_favorited: Optional[bool] = False  # 收藏状态
    is_updated: Optional[bool] = False  # 是否已更新标记
    movie_updated_at: Optional[datetime] = None  # 影片最后更新时间

    model_config = {"from_attributes": True}


class MovieListResponse(BaseResponse):
    """影片列表响应模型"""
    data: Optional[List[MovieListItem]] = None
    total_count: Optional[int] = None
    limit: Optional[int] = None
    offset: Optional[int] = None


class MovieFilterParams(BaseModel):
    """影片过滤参数模型"""
    limit: int = Field(default=50, ge=1, le=100, description="每页数量")
    offset: int = Field(default=0, ge=0, description="偏移量")
    search: Optional[str] = Field(default=None, description="搜索关键词（标题、导演、演员）")
    genre_ids: Optional[List[int]] = Field(default=None, description="分类ID列表过滤")
    series_ids: Optional[List[int]] = Field(default=None, description="系列ID列表过滤")
    tag_ids: Optional[List[int]] = Field(default=None, description="标签ID列表过滤")
    actor_ids: Optional[List[int]] = Field(default=None, description="演员ID列表过滤")
    year: Optional[int] = Field(default=None, ge=1900, le=2100, description="年份过滤")
    year_from: Optional[int] = Field(default=None, ge=1900, le=2100, description="起始年份过滤")
    year_to: Optional[int] = Field(default=None, ge=1900, le=2100, description="结束年份过滤")
    rating_min: Optional[float] = Field(default=None, ge=0, le=10, description="最低评分过滤")
    rating_from: Optional[float] = Field(default=None, ge=0, le=10, description="起始评分过滤")
    rating_to: Optional[float] = Field(default=None, ge=0, le=10, description="结束评分过滤")
    directory_id: Optional[int] = Field(default=None, description="目录ID过滤")
    is_updated: Optional[bool] = Field(default=None, description="是否已更新过滤")
    sort_by: Optional[str] = Field(default="title", description="排序字段")
    sort_order: Optional[str] = Field(default="asc", description="排序方向")


class BatchAssociationRequest(BaseModel):
    """批量关联操作请求模型"""
    movie_ids: List[int] = Field(..., min_items=1, description="影片ID列表")
    tag_ids: Optional[List[int]] = Field(None, description="要操作的标签ID列表")
    genre_ids: Optional[List[int]] = Field(None, description="要操作的分类ID列表")
    series_id: Optional[int] = Field(None, description="要操作的系列ID")


class FavoriteFilterParams(BaseModel):
    """收藏过滤参数模型"""
    limit: int = Field(default=50, ge=1, le=100, description="每页数量")
    offset: int = Field(default=0, ge=0, description="偏移量")
    search: Optional[str] = Field(default=None, description="搜索关键词（标题、导演、演员）")
    genre_ids: Optional[List[int]] = Field(default=None, description="分类ID列表过滤")
    series_ids: Optional[List[int]] = Field(default=None, description="系列ID列表过滤")
    tag_ids: Optional[List[int]] = Field(default=None, description="标签ID列表过滤")
    year_from: Optional[int] = Field(default=None, ge=1900, le=2100, description="起始年份过滤")
    year_to: Optional[int] = Field(default=None, ge=1900, le=2100, description="结束年份过滤")
    rating_from: Optional[float] = Field(default=None, ge=0, le=10, description="起始评分过滤")
    rating_to: Optional[float] = Field(default=None, ge=0, le=10, description="结束评分过滤")
    sort_by: Optional[str] = Field(default="created_at", description="排序字段")
    sort_order: Optional[str] = Field(default="desc", description="排序方向")


# ========== 收藏相关 Schema ==========

class FavoriteResponse(BaseModel):
    """收藏响应模型"""
    id: int
    movie_id: int
    created_at: datetime
    updated_at: datetime

    # 关联的影片信息
    movie: Optional[MovieListItem] = None

    model_config = {"from_attributes": True}


class FavoriteStatusResponse(BaseModel):
    """收藏状态响应模型"""
    movie_id: int
    is_favorited: bool
    favorite_id: Optional[int] = None
    favorited_at: Optional[datetime] = None


class BatchFavoriteRequest(BaseModel):
    """批量收藏请求模型"""
    movie_ids: List[int] = Field(..., min_length=1, max_length=100, description="影片ID列表")


class DeleteFavoriteRequest(BaseModel):
    """删除收藏请求模型"""
    movie_ids: List[int] = Field(..., min_length=1, max_length=100, description="要删除收藏的影片ID列表")


class BatchFavoriteResponse(BaseModel):
    """批量收藏响应模型"""
    success_count: int
    failed_count: int
    success_movie_ids: List[int]
    failed_movie_ids: List[int]
    errors: List[str] = []


class FavoriteListResponse(BaseResponse):
    """收藏列表响应模型"""
    data: Optional[List[FavoriteResponse]] = None
    total_count: Optional[int] = None
    limit: Optional[int] = None
    offset: Optional[int] = None


# 映射规则相关模型
class MappingRuleCreate(BaseModel):
    """创建映射规则请求模型"""
    original_value: str = Field(..., min_length=1, max_length=255, description="原始值")
    mapped_value: Optional[str] = Field(default=None, max_length=255, description="映射值，为空表示删除")

    @field_validator('original_value')
    @classmethod
    def validate_original_value(cls, v):
        if not v or not v.strip():
            raise ValueError('原始值不能为空')
        return v.strip()

    @field_validator('mapped_value')
    @classmethod
    def validate_mapped_value(cls, v):
        return v.strip() if v else v


class MappingRuleUpdate(BaseModel):
    """更新映射规则请求模型"""
    original_value: Optional[str] = Field(default=None, min_length=1, max_length=255, description="原始值")
    mapped_value: Optional[str] = Field(default=None, max_length=255, description="映射值，为空表示删除")

    @field_validator('original_value')
    @classmethod
    def validate_original_value(cls, v):
        if v is not None and (not v or not v.strip()):
            raise ValueError('原始值不能为空')
        return v.strip() if v else v

    @field_validator('mapped_value')
    @classmethod
    def validate_mapped_value(cls, v):
        return v.strip() if v else v


class MappingRuleResponse(BaseModel):
    """映射规则响应模型"""
    id: int
    type: str
    original_value: str
    mapped_value: Optional[str]
    status: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class MappingRuleBatchCreate(BaseModel):
    """批量创建映射规则请求模型"""
    rules: List[MappingRuleCreate] = Field(..., min_items=1, description="映射规则列表")
    overwrite: bool = Field(default=False, description="是否覆盖已存在的规则")


class MappingRuleBatchResponse(BaseModel):
    """批量操作映射规则响应模型"""
    success_count: int
    failed_count: int
    success_rules: List[MappingRuleResponse]
    failed_rules: List[dict]
    errors: List[str] = []


class MappingRuleListResponse(BaseResponse):
    """映射规则列表响应模型"""
    data: Optional[List[MappingRuleResponse]] = None
    total_count: Optional[int] = None
    limit: Optional[int] = None
    offset: Optional[int] = None


class MappingRuleListRequest(BaseModel):
    """映射规则列表查询请求模型"""
    limit: int = Field(default=50, ge=1, le=1000, description="每页数量，范围1-1000")
    offset: int = Field(default=0, ge=0, description="偏移量，必须大于等于0")
    search: Optional[str] = Field(default=None, max_length=255, description="搜索关键词")
    status: Optional[str] = Field(default=None, description="状态过滤，可选值：active、empty")

    @field_validator('status')
    @classmethod
    def validate_status(cls, v):
        if v is not None and v not in ["active", "empty"]:
            raise ValueError("状态参数必须是 active 或 empty")
        return v

    @field_validator('search')
    @classmethod
    def validate_search(cls, v):
        return v.strip() if v else v


class MappingRuleDeleteRequest(BaseModel):
    """映射规则批量删除请求模型"""
    mappings_ids: List[int] = Field(..., description="要删除的映射规则ID列表", min_items=1)


class MappingRuleExportResponse(BaseModel):
    """映射规则导出响应模型"""
    type: str
    rules: List[dict]
    exported_at: datetime
    total_count: int


class ImageListRequest(BaseModel):
    """图片列表请求模型"""
    image_type: Optional[str] = Field(None, description="图片类型过滤")
    limit: int = Field(50, ge=1, le=1000, description="返回数量限制")
    offset: int = Field(0, ge=0, description="偏移量")


class DirectoryListRequest(BaseModel):
    """目录列表请求模型"""
    enabled_only: bool = Field(False, description="是否只返回启用的目录")
    with_cover: bool = Field(False, description="是否包含封面图片")


class DirectoryDeleteRequest(BaseModel):
    """目录批量删除请求模型"""
    directories_ids: List[int] = Field(..., description="要删除的目录ID列表", min_items=1)


class DirectoryScanRequest(BaseModel):
    """目录批量扫描请求模型"""
    directories_ids: List[int] = Field(..., description="要扫描的目录ID列表，空列表表示扫描所有启用的目录", default_factory=list)
    incremental: bool = Field(True, description="是否进行增量扫描")


# 目录路径验证相关模型
class DirectoryPathValidateRequest(BaseModel):
    """路径验证请求模型"""
    path: str = Field(..., min_length=1, max_length=500, description="要验证的路径")

    @field_validator('path')
    @classmethod
    def validate_path(cls, v):
        if not v or not v.strip():
            raise ValueError('路径不能为空')
        return v.strip()


class DirectoryPathValidateResponse(BaseModel):
    """路径验证响应模型"""
    is_valid: bool
    normalized_path: str
    exists: bool
    is_directory: bool
    is_duplicate: bool
    existing_directory_name: Optional[str] = None
    error: Optional[str] = None


class MovieDeleteRequest(BaseModel):
    """电影批量删除请求模型"""
    movies_ids: List[int] = Field(..., description="要删除的电影ID列表", min_items=1)
    force: bool = Field(False, description="是否强制删除")





# 配置相关模型
class ConfigCreate(BaseModel):
    """创建配置请求模型"""
    config_key: str = Field(..., min_length=1, max_length=100, description="配置键名")
    config_value: Optional[str] = Field(default=None, description="配置值")
    description: Optional[str] = Field(default=None, description="配置描述")

    @field_validator('config_key')
    @classmethod
    def validate_config_key(cls, v):
        if not v or not v.strip():
            raise ValueError('配置键名不能为空')
        return v.strip()


class ConfigUpdate(BaseModel):
    """更新配置请求模型"""
    config_value: Optional[str] = Field(default=None, description="配置值")
    description: Optional[str] = Field(default=None, description="配置描述")


class ConfigResponse(BaseModel):
    """配置响应模型"""
    id: int
    config_key: str
    config_value: Optional[str]
    description: Optional[str]
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}


class VideoExtensionsConfigRequest(BaseModel):
    """视频扩展名配置请求模型"""
    extensions: List[str] = Field(..., min_length=1, description="视频扩展名列表")

    @field_validator('extensions')
    @classmethod
    def validate_extensions(cls, v):
        if not v:
            raise ValueError('扩展名列表不能为空')

        validated_extensions = []
        for ext in v:
            if not ext or not ext.strip():
                continue

            ext = ext.strip().lower()

            # 确保以点号开头
            if not ext.startswith('.'):
                ext = '.' + ext

            # 验证扩展名格式（只允许字母和数字）
            ext_without_dot = ext[1:]
            if not ext_without_dot.isalnum():
                raise ValueError(f'扩展名格式无效: {ext}')

            validated_extensions.append(ext)

        if not validated_extensions:
            raise ValueError('没有有效的扩展名')

        # 去重并保持顺序
        seen = set()
        unique_extensions = []
        for ext in validated_extensions:
            if ext not in seen:
                seen.add(ext)
                unique_extensions.append(ext)

        return unique_extensions


class VideoExtensionsConfigResponse(BaseModel):
    """视频扩展名配置响应模型"""
    extensions: List[str]
    default_extensions: List[str]


# ========== 合并功能相关模型 ==========

class MergeRequest(BaseModel):
    """合并请求模型"""
    source_ids: List[int] = Field(..., min_items=2, description="要合并的源项目ID列表，至少需要2个")
    target_name: str = Field(..., min_length=1, max_length=255, description="合并后的目标名称")
    target_description: Optional[str] = Field(default=None, max_length=1000, description="合并后的目标描述")

    @field_validator('source_ids')
    @classmethod
    def validate_source_ids(cls, v):
        if len(v) < 2:
            raise ValueError('至少需要选择2个项目进行合并')
        # 去重并保持顺序
        seen = set()
        unique_ids = []
        for id_val in v:
            if id_val not in seen:
                seen.add(id_val)
                unique_ids.append(id_val)
        if len(unique_ids) < 2:
            raise ValueError('去重后至少需要2个不同的项目ID')
        return unique_ids

    @field_validator('target_name')
    @classmethod
    def validate_target_name(cls, v):
        if not v or not v.strip():
            raise ValueError('目标名称不能为空')
        return v.strip()


class MergeConflictInfo(BaseModel):
    """合并冲突信息模型"""
    existing_item_id: int = Field(..., description="已存在项目的ID")
    existing_item_name: str = Field(..., description="已存在项目的名称")
    movie_count: int = Field(..., description="已存在项目关联的影片数量")


class MergeCheckResponse(BaseModel):
    """合并检查响应模型"""
    can_merge: bool = Field(..., description="是否可以直接合并")
    conflict_info: Optional[MergeConflictInfo] = Field(default=None, description="冲突信息，如果存在同名项目")
    message: str = Field(..., description="检查结果消息")


class MergeResult(BaseModel):
    """合并结果模型"""
    success: bool = Field(..., description="合并是否成功")
    target_item_id: int = Field(..., description="合并后目标项目的ID")
    target_item_name: str = Field(..., description="合并后目标项目的名称")
    merged_count: int = Field(..., description="成功合并的项目数量")
    total_movies_affected: int = Field(..., description="受影响的影片总数")
    message: str = Field(..., description="合并结果消息")


class RenameConflictCheckRequest(BaseModel):
    """重命名冲突检查请求模型"""
    item_id: int = Field(..., description="要重命名的项目ID")
    new_name: str = Field(..., min_length=1, max_length=255, description="新名称")

    @field_validator('new_name')
    @classmethod
    def validate_new_name(cls, v):
        if not v or not v.strip():
            raise ValueError('新名称不能为空')
        return v.strip()


class RenameConflictCheckResponse(BaseModel):
    """重命名冲突检查响应模型"""
    has_conflict: bool = Field(..., description="是否存在命名冲突")
    conflict_info: Optional[MergeConflictInfo] = Field(default=None, description="冲突信息")
    can_auto_merge: bool = Field(..., description="是否可以自动合并")
    message: str = Field(..., description="检查结果消息")


# ========== 翻译功能相关模型 ==========

class TranslationConfigRequest(BaseModel):
    """翻译配置请求模型"""
    api_url: Optional[str] = Field(default=None, description="ChatGPT API 兼容 URL")
    api_key: Optional[str] = Field(default=None, description="API Key")
    model_name: Optional[str] = Field(default=None, description="模型名称")
    prompt_template: Optional[str] = Field(default=None, description="翻译提示词模板")
    source_language: Optional[str] = Field(default=None, description="翻译源语言")
    target_language: Optional[str] = Field(default=None, description="翻译目标语言")
    enabled: Optional[bool] = Field(default=None, description="是否启用翻译功能")

    @field_validator('api_url')
    @classmethod
    def validate_api_url(cls, v):
        if v is not None and v.strip():
            v = v.strip()
            if not (v.startswith('http://') or v.startswith('https://')):
                raise ValueError('API URL 必须以 http:// 或 https:// 开头')
        return v

    @field_validator('prompt_template')
    @classmethod
    def validate_prompt_template(cls, v):
        if v is not None and v.strip():
            v = v.strip()
            if '{text}' not in v:
                raise ValueError('提示词模板必须包含 {text} 占位符')
            if '{target_language}' not in v:
                raise ValueError('提示词模板必须包含 {target_language} 占位符')
            if '{source_language}' not in v:
                raise ValueError('提示词模板必须包含 {source_language} 占位符')
        return v


class TranslationConfigResponse(BaseModel):
    """翻译配置响应模型"""
    api_url: str
    api_key: str
    model_name: str
    prompt_template: str
    source_language: str
    target_language: str
    enabled: bool


class TranslationRequest(BaseModel):
    """翻译请求模型"""
    text: str = Field(..., min_length=1, description="要翻译的文本")
    field_name: Optional[str] = Field(default="", description="字段名称")

    @field_validator('text')
    @classmethod
    def validate_text(cls, v):
        if not v or not v.strip():
            raise ValueError('翻译文本不能为空')
        return v.strip()


class BatchTranslationRequest(BaseModel):
    """批量翻译请求模型"""
    fields: Dict[str, str] = Field(..., description="要翻译的字段字典")

    @field_validator('fields')
    @classmethod
    def validate_fields(cls, v):
        if not v:
            raise ValueError('翻译字段不能为空')
        if len(v) == 0:
            raise ValueError('翻译字段不能为空')
        # 过滤掉空值
        filtered_fields = {k: v for k, v in v.items() if v and v.strip()}
        if not filtered_fields:
            raise ValueError('至少需要一个非空的翻译字段')
        return filtered_fields


class TranslationResult(BaseModel):
    """翻译结果模型"""
    success: bool = Field(..., description="翻译是否成功")
    original_text: str = Field(..., description="原始文本")
    translated_text: str = Field(..., description="翻译后的文本")
    error: Optional[str] = Field(default=None, description="错误信息")


class BatchTranslationResponse(BaseModel):
    """批量翻译响应模型"""
    results: Dict[str, TranslationResult] = Field(..., description="翻译结果字典")
    total_count: int = Field(..., description="总翻译字段数")
    success_count: int = Field(..., description="成功翻译字段数")
    failed_count: int = Field(..., description="失败翻译字段数")
