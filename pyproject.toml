[project]
name = "fastapi-mdc"
version = "0.9.34"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "aiohttp>=3.12.15",
    "alembic>=1.16.4",
    "fastapi>=0.116.1",
    "jinja2>=3.1.6",
    "pillow>=10.0.0",
    "python-multipart>=0.0.20",
    "requests>=2.32.4",
    "sqlalchemy>=2.0.41",
    "toml>=0.10.2",
    "uvicorn>=0.35.0",
]

[dependency-groups]
dev = [
    "httpx>=0.28.1",
    "pytest>=8.4.1",
]
