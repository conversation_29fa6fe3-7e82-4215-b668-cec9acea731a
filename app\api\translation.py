"""
翻译管理 API 路由
提供翻译功能和配置管理接口
"""
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.services.translation_service import TranslationService
from app.schemas.schemas import (
    TranslationConfigRequest, TranslationConfigResponse, BaseResponse,
    TranslationRequest, BatchTranslationRequest, TranslationResult, BatchTranslationResponse
)
import logging

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(
    prefix="/translation",
    tags=["翻译管理"],
    responses={404: {"description": "配置不存在"}}
)


@router.get("/config", response_model=BaseResponse)
async def get_translation_config(db: Session = Depends(get_db)):
    """
    获取翻译配置
    
    Args:
        db: 数据库会话
        
    Returns:
        翻译配置信息
    """
    try:
        translation_service = TranslationService(db)
        config = translation_service.get_translation_config()
        
        # 隐藏 API Key 的敏感信息
        safe_config = config.copy()
        if safe_config['api_key']:
            safe_config['api_key'] = '*' * 8 + safe_config['api_key'][-4:] if len(safe_config['api_key']) > 4 else '*' * len(safe_config['api_key'])
        
        response_data = TranslationConfigResponse(**safe_config)
        
        return BaseResponse(
            success=True,
            message="获取翻译配置成功",
            data=response_data
        )
        
    except Exception as e:
        logger.error(f"获取翻译配置时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取翻译配置失败: {str(e)}")


@router.post("/config", response_model=BaseResponse)
async def update_translation_config(config_data: TranslationConfigRequest, db: Session = Depends(get_db)):
    """
    更新翻译配置
    
    Args:
        config_data: 翻译配置数据
        db: 数据库会话
        
    Returns:
        更新结果
    """
    try:
        translation_service = TranslationService(db)
        
        # 构建配置字典，只包含非空值
        config_dict = {}
        if config_data.api_url is not None:
            config_dict['api_url'] = config_data.api_url
        if config_data.api_key is not None:
            config_dict['api_key'] = config_data.api_key
        if config_data.model_name is not None:
            config_dict['model_name'] = config_data.model_name
        if config_data.prompt_template is not None:
            config_dict['prompt_template'] = config_data.prompt_template
        if config_data.source_language is not None:
            config_dict['source_language'] = config_data.source_language
        if config_data.target_language is not None:
            config_dict['target_language'] = config_data.target_language
        if config_data.enabled is not None:
            config_dict['enabled'] = config_data.enabled
        
        success = translation_service.set_translation_config(config_dict)
        
        if not success:
            raise HTTPException(status_code=500, detail="更新翻译配置失败")
        
        # 返回更新后的配置（隐藏敏感信息）
        updated_config = translation_service.get_translation_config()
        safe_config = updated_config.copy()
        if safe_config['api_key']:
            safe_config['api_key'] = '*' * 8 + safe_config['api_key'][-4:] if len(safe_config['api_key']) > 4 else '*' * len(safe_config['api_key'])
        
        response_data = TranslationConfigResponse(**safe_config)
        
        return BaseResponse(
            success=True,
            message="翻译配置更新成功",
            data=response_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新翻译配置时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"更新翻译配置失败: {str(e)}")


@router.post("/translate", response_model=BaseResponse)
async def translate_text(request: TranslationRequest, db: Session = Depends(get_db)):
    """
    翻译单个文本
    
    Args:
        request: 翻译请求
        db: 数据库会话
        
    Returns:
        翻译结果
    """
    try:
        translation_service = TranslationService(db)
        result = await translation_service.translate_text(request.text, request.field_name)
        
        response_data = TranslationResult(
            success=result['success'],
            original_text=result.get('original_text', request.text),
            translated_text=result['translated_text'],
            error=result.get('error')
        )
        
        return BaseResponse(
            success=True,
            message="翻译完成",
            data=response_data
        )
        
    except Exception as e:
        logger.error(f"翻译文本时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"翻译失败: {str(e)}")


@router.post("/translate/batch", response_model=BaseResponse)
async def translate_batch(request: BatchTranslationRequest, db: Session = Depends(get_db)):
    """
    批量翻译多个字段
    
    Args:
        request: 批量翻译请求
        db: 数据库会话
        
    Returns:
        批量翻译结果
    """
    try:
        translation_service = TranslationService(db)
        results = await translation_service.translate_multiple_fields(request.fields)
        
        # 构建响应数据
        translation_results = {}
        success_count = 0
        failed_count = 0
        
        for field_name, result in results.items():
            translation_result = TranslationResult(
                success=result['success'],
                original_text=result.get('original_text', request.fields.get(field_name, '')),
                translated_text=result['translated_text'],
                error=result.get('error')
            )
            translation_results[field_name] = translation_result
            
            if result['success']:
                success_count += 1
            else:
                failed_count += 1
        
        response_data = BatchTranslationResponse(
            results=translation_results,
            total_count=len(request.fields),
            success_count=success_count,
            failed_count=failed_count
        )
        
        return BaseResponse(
            success=True,
            message=f"批量翻译完成，成功 {success_count} 个，失败 {failed_count} 个",
            data=response_data
        )
        
    except Exception as e:
        logger.error(f"批量翻译时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"批量翻译失败: {str(e)}")


@router.post("/test", response_model=BaseResponse)
async def test_translation_config(db: Session = Depends(get_db)):
    """
    测试翻译配置
    
    Args:
        db: 数据库会话
        
    Returns:
        测试结果
    """
    try:
        translation_service = TranslationService(db)
        
        # 使用简单的测试文本
        test_text = "Hello, World!"
        result = await translation_service.translate_text(test_text, "test")
        
        if result['success']:
            return BaseResponse(
                success=True,
                message="翻译配置测试成功",
                data={
                    'test_text': test_text,
                    'translated_text': result['translated_text']
                }
            )
        else:
            return BaseResponse(
                success=False,
                message=f"翻译配置测试失败: {result.get('error', '未知错误')}",
                data=None
            )
        
    except Exception as e:
        logger.error(f"测试翻译配置时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"测试翻译配置失败: {str(e)}")


@router.get("/status", response_model=BaseResponse)
async def get_translation_status(db: Session = Depends(get_db)):
    """
    获取翻译服务状态
    
    Args:
        db: 数据库会话
        
    Returns:
        翻译服务状态
    """
    try:
        translation_service = TranslationService(db)
        config = translation_service.get_translation_config()
        
        # 检查配置完整性
        is_configured = bool(
            config['api_url'] and 
            config['api_key'] and 
            config['prompt_template'] and 
            config['target_language']
        )
        
        status_data = {
            'enabled': config['enabled'],
            'configured': is_configured,
            'api_url_set': bool(config['api_url']),
            'api_key_set': bool(config['api_key']),
            'prompt_template_set': bool(config['prompt_template']),
            'target_language_set': bool(config['target_language'])
        }
        
        return BaseResponse(
            success=True,
            message="获取翻译服务状态成功",
            data=status_data
        )
        
    except Exception as e:
        logger.error(f"获取翻译服务状态时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取翻译服务状态失败: {str(e)}")
