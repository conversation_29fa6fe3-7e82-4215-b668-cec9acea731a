"""
系列管理 API 路由
提供系列的 CRUD 操作接口
"""
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.services.management_service import SeriesService
from app.schemas.schemas import (
    SeriesCreate, SeriesUpdate, SeriesResponse, SeriesListResponse, SeriesListRequest, BaseResponse,
    BatchDeleteRequest, BatchDeleteResponse, UnifiedDeleteRequest,
    MergeRequest, MergeResult, MergeCheckResponse,
    RenameConflictCheckRequest, RenameConflictCheckResponse
)
from typing import List
import logging

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(
    prefix="/series",
    tags=["系列管理"],
    responses={404: {"description": "系列不存在"}}
)


@router.post("/add", response_model=BaseResponse)
async def create_series(series_data: SeriesCreate, db: Session = Depends(get_db)):
    """
    创建新系列
    
    Args:
        series_data: 系列创建数据
        db: 数据库会话
        
    Returns:
        创建结果
    """
    try:
        series_service = SeriesService(db)
        series = series_service.create_series(series_data)
        
        if not series:
            raise HTTPException(status_code=400, detail="系列名称已存在")
        
        return BaseResponse(
            success=True,
            message="系列创建成功",
            data=SeriesResponse.model_validate(series)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建系列时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"创建系列失败: {str(e)}")


@router.post("/list", response_model=SeriesListResponse)
async def get_series(request: SeriesListRequest, db: Session = Depends(get_db)):
    """
    获取系列列表

    Args:
        request: 系列列表请求参数
        db: 数据库会话

    Returns:
        系列列表
    """
    try:
        # 参数验证
        if request.limit < 1 or request.limit > 1000:
            raise HTTPException(status_code=400, detail="limit 必须在 1-1000 之间")
        if request.offset < 0:
            raise HTTPException(status_code=400, detail="offset 必须大于等于 0")

        series_service = SeriesService(db)
        series_with_counts, total_count = series_service.get_series_list_with_movie_counts(
            limit=request.limit,
            offset=request.offset,
            search=request.search
        )

        # 构建响应数据
        series_responses = []
        for series_info in series_with_counts:
            series_response = SeriesResponse.model_validate(series_info["series"])
            series_response.movie_count = series_info["movie_count"]
            series_responses.append(series_response)

        return SeriesListResponse(
            success=True,
            message=f"获取到 {len(series_with_counts)} 个系列",
            data=series_responses,
            total_count=total_count,
            limit=request.limit,
            offset=request.offset
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取系列列表时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取系列列表失败: {str(e)}")


@router.get("/info/{series_id}", response_model=BaseResponse)
async def get_series_detail(series_id: int, db: Session = Depends(get_db)):
    """
    获取指定系列详情
    
    Args:
        series_id: 系列 ID
        db: 数据库会话
        
    Returns:
        系列详情
    """
    try:
        series_service = SeriesService(db)
        series_info = series_service.get_series_with_movie_count(series_id)
        
        if not series_info:
            raise HTTPException(status_code=404, detail="系列不存在")
        
        series_response = SeriesResponse.model_validate(series_info["series"])
        series_response.movie_count = series_info["movie_count"]
        
        return BaseResponse(
            success=True,
            message="获取系列详情成功",
            data=series_response
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取系列详情时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取系列详情失败: {str(e)}")


@router.put("/edit/{series_id}", response_model=BaseResponse)
async def update_series(series_id: int, series_data: SeriesUpdate, db: Session = Depends(get_db)):
    """
    更新系列信息
    
    Args:
        series_id: 系列 ID
        series_data: 更新数据
        db: 数据库会话
        
    Returns:
        更新结果
    """
    try:
        series_service = SeriesService(db)
        series = series_service.update_series(series_id, series_data)
        
        if not series:
            raise HTTPException(status_code=404, detail="系列不存在")
        
        return BaseResponse(
            success=True,
            message="系列更新成功",
            data=SeriesResponse.model_validate(series)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新系列时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"更新系列失败: {str(e)}")


@router.delete("", response_model=BaseResponse)
async def delete_series(request: UnifiedDeleteRequest, db: Session = Depends(get_db)):
    """
    统一删除系列（支持单项和批量删除）

    Args:
        request: 统一删除请求，支持单个ID或ID列表
        db: 数据库会话

    Returns:
        删除结果
    """
    try:
        series_service = SeriesService(db)
        result = series_service.unified_delete_series(request)

        if isinstance(result, tuple):
            # 单项删除结果
            success, message = result
            if not success:
                if "不存在" in message:
                    raise HTTPException(status_code=404, detail=message)
                else:
                    raise HTTPException(status_code=400, detail=message)

            return BaseResponse(
                success=True,
                message=message
            )
        else:
            # 批量删除结果
            batch_result = result
            if batch_result.failed_count == 0:
                message = f"成功删除 {batch_result.success_count} 个系列"
            elif batch_result.success_count == 0:
                message = f"删除失败，{batch_result.failed_count} 个系列无法删除"
            else:
                message = f"成功删除 {batch_result.success_count} 个系列，{batch_result.failed_count} 个失败"

            return BaseResponse(
                success=True,
                message=message,
                data=batch_result
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除系列时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"删除系列失败: {str(e)}")


@router.post("/check-rename-conflict", response_model=RenameConflictCheckResponse)
async def check_rename_conflict(request: RenameConflictCheckRequest, db: Session = Depends(get_db)):
    """
    检查系列重命名是否会产生冲突

    Args:
        request: 重命名冲突检查请求
        db: 数据库会话

    Returns:
        冲突检查结果
    """
    try:
        series_service = SeriesService(db)
        result = series_service.check_rename_conflict(request.item_id, request.new_name)

        return RenameConflictCheckResponse(
            has_conflict=result["has_conflict"],
            can_auto_merge=result["can_auto_merge"],
            conflict_info=result.get("conflict_info"),
            message=result["message"]
        )

    except Exception as e:
        logger.error(f"检查系列重命名冲突时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"检查失败: {str(e)}")


@router.post("/check-merge-conflict", response_model=MergeCheckResponse)
async def check_merge_conflict(request: dict, db: Session = Depends(get_db)):
    """
    检查系列合并是否会产生冲突

    Args:
        request: 包含 target_name 和 source_ids 的请求体
        db: 数据库会话

    Returns:
        合并冲突检查结果
    """
    try:
        target_name = request.get("target_name")
        source_ids = request.get("source_ids")

        if not target_name:
            raise HTTPException(status_code=400, detail="target_name 是必需的")
        if not source_ids or not isinstance(source_ids, list):
            raise HTTPException(status_code=400, detail="source_ids 必须是非空列表")

        series_service = SeriesService(db)
        result = series_service.check_merge_conflict(target_name, source_ids)

        return MergeCheckResponse(
            can_merge=result["can_merge"],
            conflict_info=result.get("conflict_info"),
            message=result["message"]
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"检查系列合并冲突时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"检查失败: {str(e)}")


@router.post("/merge", response_model=MergeResult)
async def merge_series(request: MergeRequest, db: Session = Depends(get_db)):
    """
    合并多个系列

    Args:
        request: 合并请求
        db: 数据库会话

    Returns:
        合并结果
    """
    try:
        series_service = SeriesService(db)
        result = series_service.merge_series(
            source_ids=request.source_ids,
            target_name=request.target_name,
            target_description=request.target_description
        )

        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["message"])

        return MergeResult(
            success=result["success"],
            target_item_id=result["target_item_id"],
            target_item_name=result["target_item_name"],
            merged_count=result["merged_count"],
            total_movies_affected=result["total_movies_affected"],
            message=result["message"]
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"合并系列时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"合并失败: {str(e)}")
