{% extends "base.html" %}

{% block title %}编辑影片 - 媒体管理器{% endblock %}

{% block page_header %}
<!-- 页面标题区域 -->
<div class="bg-gradient-to-r from-primary/10 to-secondary/10 border-b border-base-300">
    <div class="container mx-auto px-4 py-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
                <div class="mb-3">
                    <a href="#" id="back-btn" class="btn btn-outline btn-sm">
                        <i class="bi bi-arrow-left" aria-label="返回图标"></i>
                        返回详情页
                    </a>
                </div>
                <h1 class="text-3xl font-bold text-base-content flex items-center gap-3" id="page-title">
                    <i class="bi bi-pencil text-primary" style="font-size: 2rem;" aria-label="编辑影片图标"></i>
                    编辑影片
                </h1>
                <p class="text-base-content/70 mt-2">修改影片信息和元数据</p>
            </div>
            <div class="flex items-center gap-3">
                <button id="save-btn" class="btn btn-primary btn-sm hidden">
                    <i class="bi bi-check" aria-label="保存图标"></i>
                    保存更改
                </button>
                <button id="cancel-btn" class="btn btn-outline btn-sm hidden">
                    <i class="bi bi-x" aria-label="取消图标"></i>
                    取消
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}

<!-- 页面内容 -->
<div class="container mx-auto px-4 py-6">
    <!-- 加载状态 -->
    <div id="loading-container" class="flex flex-col items-center justify-center py-16">
        <span class="loading loading-spinner loading-lg text-primary"></span>
        <div class="mt-4 text-base-content">正在加载影片信息...</div>
    </div>

    <!-- 错误状态 -->
    <div id="error-container" class="alert alert-error hidden">
        <i class="bi bi-exclamation-circle stroke-current shrink-0" style="font-size: 1.5rem;" aria-label="错误图标"></i>
        <div>
            <h3 class="font-bold">加载失败</h3>
            <div class="text-xs" id="error-message">无法加载影片信息，请稍后重试。</div>
        </div>
    </div>

    <!-- 编辑表单 -->
    <div id="edit-form-container" class="hidden">
        <form id="movie-edit-form" class="space-y-6">
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                <!-- 左侧：海报图片 -->
                <div class="lg:col-span-1">
                    <div class="card bg-base-100 shadow-lg">
                        <figure class="p-0">
                            <img id="movie-poster" src="" alt="" class="w-full aspect-[2/3] object-cover rounded-2xl">
                        </figure>
                        <div class="card-body p-4">
                            <h3 class="text-sm font-semibold mb-2">海报信息</h3>
                            <div class="text-xs text-base-content/70">
                                <div id="poster-info">暂无海报</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧：编辑表单 -->
                <div class="lg:col-span-3">
                    <div class="card bg-base-100 shadow-lg">
                        <div class="card-body">
                            <!-- 基本信息 -->
                            <div class="mb-6">
                                <div class="flex justify-between items-center mb-4">
                                    <h3 class="text-lg font-semibold text-base-content">基本信息</h3>
                                    <button type="button" class="btn btn-primary btn-sm batch-translation-btn" id="batch-translate-movie-btn">
                                        <span class="loading loading-spinner loading-sm mr-2 hidden"></span>
                                        <i class="bi bi-translate mr-2"></i>
                                        批量翻译
                                    </button>
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <!-- 影片标题 -->
                                    <div class="form-control">
                                        <label class="label">
                                            <span class="label-text font-medium">影片标题 <span class="text-error">*</span></span>
                                        </label>
                                        <div class="join w-full">
                                            <input type="text" class="input input-bordered join-item flex-1" id="title" name="title" placeholder="请输入影片标题" required>
                                            <button type="button" class="btn btn-outline join-item translation-btn" data-field="影片标题" data-target="title">
                                                <span class="loading loading-spinner loading-xs mr-1 hidden"></span>
                                                <i class="bi bi-translate mr-1"></i>
                                                翻译
                                            </button>
                                        </div>
                                        <div class="label">
                                            <span class="label-text-alt text-error hidden" id="title-error"></span>
                                        </div>
                                    </div>

                                    <!-- 原始标题 -->
                                    <div class="form-control">
                                        <label class="label">
                                            <span class="label-text font-medium">原始标题</span>
                                        </label>
                                        <input type="text" class="input input-bordered w-full" id="original_title" name="original_title" placeholder="请输入原始标题">
                                        <div class="label">
                                            <span class="label-text-alt text-error hidden" id="original_title-error"></span>
                                        </div>
                                    </div>

                                    <!-- 年份 -->
                                    <div class="form-control">
                                        <label class="label">
                                            <span class="label-text font-medium">年份</span>
                                        </label>
                                        <input type="number" class="input input-bordered w-full" id="year" name="year" placeholder="请输入年份" min="1900" max="2100">
                                        <div class="label">
                                            <span class="label-text-alt text-error hidden" id="year-error"></span>
                                        </div>
                                    </div>

                                    <!-- 评分 -->
                                    <div class="form-control">
                                        <label class="label">
                                            <span class="label-text font-medium">评分</span>
                                        </label>
                                        <input type="number" class="input input-bordered w-full" id="rating" name="rating" placeholder="请输入评分" min="0" max="10" step="0.1">
                                        <div class="label">
                                            <span class="label-text-alt text-error hidden" id="rating-error"></span>
                                        </div>
                                    </div>

                                    <!-- 时长 -->
                                    <div class="form-control">
                                        <label class="label">
                                            <span class="label-text font-medium">时长（分钟）</span>
                                        </label>
                                        <input type="number" class="input input-bordered w-full" id="runtime" name="runtime" placeholder="请输入时长" min="0">
                                        <div class="label">
                                            <span class="label-text-alt text-error hidden" id="runtime-error"></span>
                                        </div>
                                    </div>

                                    <!-- 国家 -->
                                    <div class="form-control">
                                        <label class="label">
                                            <span class="label-text font-medium">国家</span>
                                        </label>
                                        <div class="join w-full">
                                            <input type="text" class="input input-bordered join-item flex-1" id="country" name="country" placeholder="请输入国家">
                                            <button type="button" class="btn btn-outline join-item translation-btn" data-field="国家" data-target="country">
                                                <span class="loading loading-spinner loading-xs mr-1 hidden"></span>
                                                <i class="bi bi-translate mr-1"></i>
                                                翻译
                                            </button>
                                        </div>
                                        <div class="label">
                                            <span class="label-text-alt text-error hidden" id="country-error"></span>
                                        </div>
                                    </div>

                                    <!-- 影评人评分 -->
                                    <div class="form-control">
                                        <label class="label">
                                            <span class="label-text font-medium">影评人评分</span>
                                        </label>
                                        <input type="number" class="input input-bordered w-full" id="critic_rating" name="critic_rating" placeholder="请输入影评人评分" min="0" max="10" step="0.1">
                                        <div class="label">
                                            <span class="label-text-alt text-error hidden" id="critic_rating-error"></span>
                                        </div>
                                    </div>

                                    <!-- 编号 -->
                                    <div class="form-control">
                                        <label class="label">
                                            <span class="label-text font-medium">编号</span>
                                        </label>
                                        <input type="text" class="input input-bordered w-full" id="num" name="num" placeholder="请输入编号">
                                        <div class="label">
                                            <span class="label-text-alt text-error hidden" id="num-error"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 简短描述 -->
                            <div class="mb-6">
                                <div class="form-control">
                                    <label class="label">
                                        <span class="label-text font-medium">简短描述</span>
                                    </label>
                                    <div class="relative">
                                        <textarea class="textarea textarea-bordered w-full h-20" id="outline" name="outline" placeholder="请输入简短描述"></textarea>
                                        <button type="button" class="btn btn-outline btn-sm absolute top-2 right-2 translation-btn" data-field="简短描述" data-target="outline">
                                            <span class="loading loading-spinner loading-xs mr-1 hidden"></span>
                                            <i class="bi bi-translate mr-1"></i>
                                            翻译
                                        </button>
                                    </div>
                                    <div class="label">
                                        <span class="label-text-alt text-error hidden" id="outline-error"></span>
                                    </div>
                                </div>
                            </div>

                            <!-- 剧情简介 -->
                            <div class="mb-6">
                                <div class="form-control">
                                    <label class="label">
                                        <span class="label-text font-medium">剧情简介</span>
                                    </label>
                                    <div class="relative">
                                        <textarea class="textarea textarea-bordered w-full h-32" id="plot" name="plot" placeholder="请输入剧情简介"></textarea>
                                        <button type="button" class="btn btn-outline btn-sm absolute top-2 right-2 translation-btn" data-field="剧情简介" data-target="plot">
                                            <span class="loading loading-spinner loading-xs mr-1 hidden"></span>
                                            <i class="bi bi-translate mr-1"></i>
                                            翻译
                                        </button>
                                    </div>
                                    <div class="label">
                                        <span class="label-text-alt text-error hidden" id="plot-error"></span>
                                    </div>
                                </div>
                            </div>

                            <!-- 预告片链接 -->
                            <div class="mb-6">
                                <div class="form-control">
                                    <label class="label">
                                        <span class="label-text font-medium">预告片链接</span>
                                    </label>
                                    <input type="url" class="input input-bordered w-full" id="trailer" name="trailer" placeholder="请输入预告片链接">
                                    <div class="label">
                                        <span class="label-text-alt text-error hidden" id="trailer-error"></span>
                                    </div>
                                </div>
                            </div>

                            <!-- 关联信息 -->
                            <div class="mb-6">
                                <h3 class="text-lg font-semibold mb-4 text-base-content">关联信息</h3>
                                <div class="space-y-4">
                                    <!-- 系列 -->
                                    <div class="form-control">
                                        <label class="label">
                                            <span class="label-text font-medium">系列 (Series)</span>
                                        </label>
                                        <div id="series-container">
                                            <div class="text-sm text-base-content/70">正在加载系列...</div>
                                        </div>
                                        <div class="label">
                                            <span class="label-text-alt text-error hidden" id="series_id-error"></span>
                                        </div>
                                    </div>

                                    <!-- 标签 -->
                                    <div class="form-control">
                                        <label class="label">
                                            <span class="label-text">标签 (Tags)</span>
                                        </label>
                                        <div id="tags-container">
                                            <div class="text-sm text-base-content/70">正在加载标签...</div>
                                        </div>
                                    </div>

                                    <!-- 分类 -->
                                    <div class="form-control">
                                        <label class="label">
                                            <span class="label-text">分类 (Genres)</span>
                                        </label>
                                        <div id="genres-container">
                                            <div class="text-sm text-base-content/70">正在加载分类...</div>
                                        </div>
                                    </div>

                                    <!-- 演员 -->
                                    <div class="form-control">
                                        <label class="label">
                                            <span class="label-text">演员 (Actors)</span>
                                        </label>
                                        <div id="actors-container">
                                            <div class="text-sm text-base-content/70">正在加载演员...</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 文件信息（只读） -->
                            <div class="mb-6">
                                <h3 class="text-lg font-semibold mb-3 text-base-content">文件信息</h3>
                                <div id="movie-file-path" class="text-base-content/70 font-mono text-sm bg-base-200 p-3 rounded-lg break-all">
                                    -
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 引入下拉框组件 -->
<script src="/static/js/multi-select-dropdown.js?v={{ app_version }}"></script>
<script src="/static/js/single-select-dropdown.js?v={{ app_version }}"></script>
<!-- 引入翻译功能 -->
<script src="/static/js/translation-helper.js?v={{ app_version }}"></script>
<!-- 引入编辑页面 JavaScript -->
<script src="/static/js/movie-edit.js?v={{ app_version }}"></script>

<script>
// 影片编辑页面初始化
document.addEventListener('DOMContentLoaded', function() {
    // 获取访问类型和参数
    const accessType = '{{ access_type }}';
    const movieId = {{ movie_id }};

    // 初始化编辑页面
    if (window.MovieEdit) {
        window.MovieEdit.init(accessType, movieId);
    }
});
</script>
{% endblock %}
