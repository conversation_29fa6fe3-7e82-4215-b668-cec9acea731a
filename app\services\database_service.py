"""
数据库集成服务
用于将媒体文件信息保存到数据库
"""
import uuid
from datetime import datetime
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from app.models.models import Movie, Tag, Genre, Actor, Series, VImage
from app.services.mapping_service import MappingService
import logging
import os
from pathlib import Path

logger = logging.getLogger(__name__)


class DatabaseService:
    """数据库集成服务类"""

    def __init__(self, db: Session, mapping_service: Optional[MappingService] = None):
        self.db = db
        self.mapping_service = mapping_service or MappingService(db)

    def get_movie_by_file_path(self, file_path: str) -> Optional[Movie]:
        """
        根据文件路径获取影片

        Args:
            file_path: 文件路径

        Returns:
            影片对象或None
        """
        try:
            return self.db.query(Movie).filter(Movie.file_path == file_path).first()
        except Exception as e:
            logger.error(f"根据文件路径获取影片时发生错误: {e}")
            return None

    def get_movies_count_by_directory(self, directory_id: int) -> int:
        """
        获取指定目录下的影片数量

        Args:
            directory_id: 目录ID

        Returns:
            影片数量
        """
        try:
            return self.db.query(Movie).filter(Movie.directory_id == directory_id).count()
        except Exception as e:
            logger.error(f"获取目录下影片数量失败: {e}")
            return 0

    def update_movie_data(self, movie_id: int, movie_data: Dict[str, Any], file_info: Dict[str, Any], directory_id: int = None) -> Optional[Movie]:
        """
        更新现有影片数据

        Args:
            movie_id: 影片ID
            movie_data: NFO 解析的电影数据
            file_info: 文件信息
            directory_id: 目录ID

        Returns:
            更新后的影片对象或None
        """
        try:
            movie = self.db.query(Movie).filter(Movie.id == movie_id).first()
            if movie:
                logger.info(f"更新现有电影记录: {movie_data.get('title', '未知')}")
                return self._update_movie(movie, movie_data, file_info, directory_id)
            else:
                logger.warning(f"要更新的影片不存在: ID {movie_id}")
                return None
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新电影数据时发生错误: {e}")
            return None
    
    def save_movie_data(self, movie_data: Dict[str, Any], file_info: Dict[str, Any], directory_id: int = None) -> Optional[Movie]:
        """
        将电影数据保存到数据库

        Args:
            movie_data: NFO 解析的电影数据
            file_info: 文件信息
            directory_id: 目录ID

        Returns:
            保存的电影对象，如果失败则返回 None
        """
        max_retries = 3
        for attempt in range(max_retries):
            try:
                # 检查电影是否已存在（基于文件路径）
                existing_movie = self.db.query(Movie).filter(
                    Movie.file_path == file_info['file_path']
                ).first()

                if existing_movie:
                    logger.info(f"电影已存在，更新记录: {movie_data.get('title', '未知')}")
                    return self._update_movie(existing_movie, movie_data, file_info, directory_id)
                else:
                    logger.info(f"创建新电影记录: {movie_data.get('title', '未知')}")
                    return self._create_movie(movie_data, file_info, directory_id)

            except Exception as e:
                self.db.rollback()
                logger.error(f"保存电影数据时发生错误 (尝试 {attempt + 1}/{max_retries}): {e}")

                if attempt == max_retries - 1:
                    logger.error(f"保存电影数据最终失败: {movie_data.get('title', '未知')}")
                    return None

                # 等待一小段时间后重试
                import time
                time.sleep(0.1)

        return None
    
    def _create_movie(self, movie_data: Dict[str, Any], file_info: Dict[str, Any], directory_id: int = None) -> Optional[Movie]:
        """创建新的电影记录"""
        try:
            # 预先创建所有需要的关联实体，避免在主事务中创建
            series = None
            if movie_data.get('series_name'):
                # 应用映射规则处理系列
                original_series = [movie_data['series_name']]
                mapped_series = self.mapping_service.apply_series_mappings(original_series)
                logger.debug(f"系列映射: {original_series} -> {mapped_series}")

                if mapped_series:  # 如果映射后还有系列名称（未被删除）
                    series = self._ensure_series_exists(mapped_series[0])

            # 应用映射规则处理标签
            tags = []
            if movie_data.get('tags'):
                original_tags = movie_data['tags']
                mapped_tags = self.mapping_service.apply_tag_mappings(original_tags)
                logger.debug(f"标签映射: {original_tags} -> {mapped_tags}")

                for tag_name in mapped_tags:
                    tag = self._ensure_tag_exists(tag_name)
                    if tag:
                        tags.append(tag)

            # 应用映射规则处理分类
            genres = []
            if movie_data.get('genres'):
                original_genres = movie_data['genres']
                mapped_genres = self.mapping_service.apply_genre_mappings(original_genres)
                logger.debug(f"分类映射: {original_genres} -> {mapped_genres}")

                for genre_name in mapped_genres:
                    genre = self._ensure_genre_exists(genre_name)
                    if genre:
                        genres.append(genre)

            # 应用映射规则处理演员
            actors = []
            if movie_data.get('actors'):
                original_actors = movie_data['actors']
                mapped_actors = self.mapping_service.apply_actor_data_mappings(original_actors)
                logger.debug(f"演员映射: {len(original_actors)} -> {len(mapped_actors)} 个演员")

                for actor_data in mapped_actors:
                    actor = self._ensure_actor_exists(actor_data)
                    if actor:
                        actors.append(actor)

            # 现在在一个干净的事务中创建电影记录
            movie = Movie(
                title=movie_data.get('title', ''),
                original_title=movie_data.get('original_title'),
                year=movie_data.get('year'),
                rating=movie_data.get('rating'),
                runtime=movie_data.get('runtime'),
                plot=movie_data.get('plot'),
                outline=movie_data.get('outline'),
                release_date=movie_data.get('release_date'),
                premiered=movie_data.get('premiered'),
                country=movie_data.get('country'),
                critic_rating=movie_data.get('critic_rating'),
                sort_title=movie_data.get('sort_title'),
                trailer=movie_data.get('trailer'),
                num=movie_data.get('num'),
                lock_data=movie_data.get('lock_data', False),
                date_added=movie_data.get('date_added'),
                is_updated=False,  # 新创建的影片默认未更新
                updated_at=None,   # 新创建的影片没有更新时间
                file_path=file_info['file_path'],
                nfo_path=file_info.get('nfo_path'),
                poster_path=file_info.get('images', {}).get('poster'),
                fanart_path=file_info.get('images', {}).get('fanart'),
                thumb_path=file_info.get('images', {}).get('thumb'),
                directory_id=directory_id
            )

            # 设置系列关联
            if series:
                movie.series = series

            self.db.add(movie)
            self.db.flush()  # 获取 movie.id

            # 处理图片虚拟化
            self._process_movie_images(movie, file_info)

            # 安全地添加关联关系
            self._safe_add_associations(movie, tags, genres, actors)

            self.db.commit()
            self.db.refresh(movie)

            logger.info(f"成功创建电影记录: {movie.title} (ID: {movie.id})")
            return movie

        except Exception as e:
            self.db.rollback()
            logger.error(f"创建电影记录时发生错误: {e}")
            return None
    
    def _update_movie(self, movie: Movie, movie_data: Dict[str, Any], file_info: Dict[str, Any], directory_id: int = None) -> Optional[Movie]:
        """更新现有的电影记录"""
        try:
            # 更新基本信息
            movie.title = movie_data.get('title', movie.title)
            movie.original_title = movie_data.get('original_title', movie.original_title)
            movie.year = movie_data.get('year', movie.year)
            movie.rating = movie_data.get('rating', movie.rating)
            movie.runtime = movie_data.get('runtime', movie.runtime)
            movie.plot = movie_data.get('plot', movie.plot)
            movie.outline = movie_data.get('outline', movie.outline)
            movie.release_date = movie_data.get('release_date', movie.release_date)
            movie.premiered = movie_data.get('premiered', movie.premiered)
            movie.country = movie_data.get('country', movie.country)
            movie.critic_rating = movie_data.get('critic_rating', movie.critic_rating)
            movie.sort_title = movie_data.get('sort_title', movie.sort_title)
            movie.trailer = movie_data.get('trailer', movie.trailer)
            movie.num = movie_data.get('num', movie.num)
            movie.lock_data = movie_data.get('lock_data', movie.lock_data)
            movie.date_added = movie_data.get('date_added', movie.date_added)
            
            # 更新文件路径
            movie.nfo_path = file_info.get('nfo_path', movie.nfo_path)
            movie.poster_path = file_info.get('images', {}).get('poster', movie.poster_path)
            movie.fanart_path = file_info.get('images', {}).get('fanart', movie.fanart_path)
            movie.thumb_path = file_info.get('images', {}).get('thumb', movie.thumb_path)

            # 更新目录关联
            if directory_id is not None:
                movie.directory_id = directory_id
            
            # 更新系列信息
            if movie_data.get('series_name'):
                # 应用映射规则处理系列
                original_series = [movie_data['series_name']]
                mapped_series = self.mapping_service.apply_series_mappings(original_series)
                logger.debug(f"更新系列映射: {original_series} -> {mapped_series}")

                if mapped_series:  # 如果映射后还有系列名称（未被删除）
                    series = self._get_or_create_series(mapped_series[0])
                    if series:
                        movie.series = series
                else:
                    # 如果映射后系列被删除，清除系列关联
                    movie.series = None
            
            # 处理图片虚拟化（更新模式）
            self._process_movie_images(movie, file_info, update_mode=True)

            # 清除并重新添加关联关系
            movie.tags.clear()
            movie.genres.clear()
            movie.actors.clear()

            # 应用映射规则并重新添加标签
            if movie_data.get('tags'):
                original_tags = movie_data['tags']
                mapped_tags = self.mapping_service.apply_tag_mappings(original_tags)
                logger.debug(f"更新标签映射: {original_tags} -> {mapped_tags}")

                for tag_name in mapped_tags:
                    tag = self._get_or_create_tag(tag_name)
                    if tag and tag not in movie.tags:
                        movie.tags.append(tag)

            # 应用映射规则并重新添加分类
            if movie_data.get('genres'):
                original_genres = movie_data['genres']
                mapped_genres = self.mapping_service.apply_genre_mappings(original_genres)
                logger.debug(f"更新分类映射: {original_genres} -> {mapped_genres}")

                for genre_name in mapped_genres:
                    genre = self._get_or_create_genre(genre_name)
                    if genre and genre not in movie.genres:
                        movie.genres.append(genre)

            # 应用映射规则并重新添加演员
            if movie_data.get('actors'):
                original_actors = movie_data['actors']
                mapped_actors = self.mapping_service.apply_actor_data_mappings(original_actors)
                logger.debug(f"更新演员映射: {len(original_actors)} -> {len(mapped_actors)} 个演员")

                for actor_data in mapped_actors:
                    actor = self._get_or_create_actor(actor_data)
                    if actor and actor not in movie.actors:
                        movie.actors.append(actor)
            
            self.db.commit()
            self.db.refresh(movie)
            
            logger.info(f"成功更新电影记录: {movie.title} (ID: {movie.id})")
            return movie
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新电影记录时发生错误: {e}")
            return None
    
    def _ensure_tag_exists(self, tag_name: str) -> Optional[Tag]:
        """确保标签存在，使用独立事务"""
        try:
            # 首先尝试查找现有标签
            tag = self.db.query(Tag).filter(Tag.name == tag_name).first()
            if tag:
                return tag

            # 如果不存在，创建新标签
            try:
                tag = Tag(name=tag_name)
                self.db.add(tag)
                self.db.commit()
                return tag
            except Exception as create_error:
                self.db.rollback()
                # 可能是并发创建，再次查找
                tag = self.db.query(Tag).filter(Tag.name == tag_name).first()
                if tag:
                    return tag
                logger.error(f"创建标签失败: {create_error}")
                return None

        except Exception as e:
            logger.error(f"处理标签时发生错误: {e}")
            return None

    def _get_or_create_tag(self, tag_name: str) -> Optional[Tag]:
        """获取或创建标签（保持向后兼容）"""
        return self._ensure_tag_exists(tag_name)
    
    def _ensure_genre_exists(self, genre_name: str) -> Optional[Genre]:
        """确保分类存在，使用独立事务"""
        try:
            # 首先尝试查找现有分类
            genre = self.db.query(Genre).filter(Genre.name == genre_name).first()
            if genre:
                return genre

            # 如果不存在，创建新分类
            try:
                genre = Genre(name=genre_name)
                self.db.add(genre)
                self.db.commit()
                return genre
            except Exception as create_error:
                self.db.rollback()
                # 可能是并发创建，再次查找
                genre = self.db.query(Genre).filter(Genre.name == genre_name).first()
                if genre:
                    return genre
                logger.error(f"创建分类失败: {create_error}")
                return None

        except Exception as e:
            logger.error(f"处理分类时发生错误: {e}")
            return None

    def _get_or_create_genre(self, genre_name: str) -> Optional[Genre]:
        """获取或创建类型（保持向后兼容）"""
        return self._ensure_genre_exists(genre_name)
    
    def _ensure_actor_exists(self, actor_data: Dict[str, str]) -> Optional[Actor]:
        """确保演员存在，使用独立事务"""
        try:
            actor_name = actor_data.get('name', '')
            if not actor_name:
                return None

            # 标准化演员姓名和角色
            normalized_name = self.mapping_service.normalize_string(actor_name)
            normalized_role = self.mapping_service.normalize_string(actor_data.get('role', ''))

            if not normalized_name:
                return None

            logger.debug(f"处理演员: 原始='{actor_name}' 标准化='{normalized_name}' 角色='{normalized_role}'")

            # 根据姓名和角色查找演员（同一演员可能在不同电影中扮演不同角色）
            actor = self.db.query(Actor).filter(
                Actor.name == normalized_name,
                Actor.role == normalized_role
            ).first()

            if actor:
                logger.debug(f"找到现有演员: {normalized_name}")
                return actor

            # 如果不存在，创建新演员
            try:
                actor = Actor(
                    name=normalized_name,
                    role=normalized_role,
                    actor_type=actor_data.get('actor_type', 'Actor')
                )
                self.db.add(actor)
                self.db.commit()
                logger.debug(f"创建新演员: {normalized_name}")
                return actor
            except Exception as create_error:
                self.db.rollback()
                # 可能是并发创建，再次查找
                actor = self.db.query(Actor).filter(
                    Actor.name == normalized_name,
                    Actor.role == normalized_role
                ).first()
                if actor:
                    return actor
                logger.error(f"创建演员失败: {create_error}")
                return None

        except Exception as e:
            logger.error(f"处理演员时发生错误: {e}")
            return None

    def _get_or_create_actor(self, actor_data: Dict[str, str]) -> Optional[Actor]:
        """获取或创建演员（保持向后兼容）"""
        return self._ensure_actor_exists(actor_data)
    
    def _ensure_series_exists(self, series_name: str) -> Optional[Series]:
        """确保系列存在，使用独立事务"""
        try:
            # 首先尝试查找现有系列
            series = self.db.query(Series).filter(Series.name == series_name).first()
            if series:
                return series

            # 如果不存在，创建新系列
            try:
                series = Series(name=series_name)
                self.db.add(series)
                self.db.commit()
                return series
            except Exception as create_error:
                self.db.rollback()
                # 可能是并发创建，再次查找
                series = self.db.query(Series).filter(Series.name == series_name).first()
                if series:
                    return series
                logger.error(f"创建系列失败: {create_error}")
                return None

        except Exception as e:
            logger.error(f"处理系列时发生错误: {e}")
            return None

    def _get_or_create_series(self, series_name: str) -> Optional[Series]:
        """获取或创建系列（保持向后兼容）"""
        return self._ensure_series_exists(series_name)

    def _safe_add_associations(self, movie: Movie, tags: List[Tag], genres: List[Genre], actors: List[Actor]):
        """安全地添加关联关系，避免重复"""
        try:
            # 添加标签关联
            for tag in tags:
                if tag not in movie.tags:
                    movie.tags.append(tag)

            # 添加分类关联
            for genre in genres:
                if genre not in movie.genres:
                    movie.genres.append(genre)

            # 添加演员关联
            for actor in actors:
                if actor not in movie.actors:
                    movie.actors.append(actor)

        except Exception as e:
            logger.error(f"添加关联关系时发生错误: {e}")
            raise
    
    def _process_movie_images(self, movie: Movie, file_info: Dict[str, Any], update_mode: bool = False):
        """
        处理电影图片的虚拟化

        Args:
            movie: 电影对象
            file_info: 文件信息，包含图片路径
            update_mode: 是否为更新模式
        """
        try:
            images = file_info.get('images', {})

            if update_mode:
                # 更新模式：删除旧的图片记录
                self._clear_movie_images(movie)

            # 处理每种类型的图片
            for image_type, image_path in images.items():
                if image_path:
                    v_image = self._create_virtual_image(image_path, image_type)
                    if v_image:
                        # 根据图片类型更新电影记录中的对应字段
                        if image_type == 'poster':
                            movie.poster_path = v_image.uuid
                        elif image_type == 'fanart':
                            movie.fanart_path = v_image.uuid
                        elif image_type == 'thumb':
                            movie.thumb_path = v_image.uuid
                        # 可以扩展支持更多图片类型

                        logger.info(f"为电影 {movie.title} 创建 {image_type} 图片虚拟映射: {v_image.uuid}")

        except Exception as e:
            logger.error(f"处理电影图片虚拟化时发生错误: {e}")

    def _create_virtual_image(self, image_path: str, image_type: str) -> Optional[VImage]:
        """
        为图片文件创建虚拟 UUID 映射

        Args:
            image_path: 图片文件的真实路径
            image_type: 图片类型（poster、fanart、thumb 等）

        Returns:
            创建的 VImage 对象，如果失败则返回 None
        """
        try:
            # 检查是否已存在相同路径的映射
            existing_image = self.db.query(VImage).filter(
                VImage.real_file_path == image_path
            ).first()

            if existing_image:
                logger.info(f"图片 {image_path} 已存在虚拟映射: {existing_image.uuid}")
                return existing_image

            # 生成新的 UUID
            image_uuid = str(uuid.uuid4())

            # 创建虚拟图片记录
            v_image = VImage(
                uuid=image_uuid,
                real_file_path=image_path,
                image_type=image_type
            )

            self.db.add(v_image)
            self.db.flush()

            logger.info(f"创建图片虚拟映射: {image_uuid} -> {image_path} ({image_type})")
            return v_image

        except Exception as e:
            logger.error(f"创建图片虚拟映射时发生错误: {e}")
            return None

    def _clear_movie_images(self, movie: Movie):
        """
        清除电影的图片虚拟映射记录

        Args:
            movie: 电影对象
        """
        try:
            # 获取电影当前的图片 UUID
            image_uuids = []
            if movie.poster_path:
                image_uuids.append(movie.poster_path)
            if movie.fanart_path:
                image_uuids.append(movie.fanart_path)
            if movie.thumb_path:
                image_uuids.append(movie.thumb_path)

            # 删除对应的虚拟映射记录
            for image_uuid in image_uuids:
                v_image = self.db.query(VImage).filter(VImage.uuid == image_uuid).first()
                if v_image:
                    self.db.delete(v_image)
                    logger.info(f"删除图片虚拟映射: {image_uuid}")

        except Exception as e:
            logger.error(f"清除电影图片映射时发生错误: {e}")

    def get_image_by_uuid(self, image_uuid: str) -> Optional[VImage]:
        """
        根据 UUID 获取图片映射记录

        Args:
            image_uuid: 图片的虚拟 UUID

        Returns:
            VImage 对象，如果不存在则返回 None
        """
        try:
            return self.db.query(VImage).filter(VImage.uuid == image_uuid).first()
        except Exception as e:
            logger.error(f"获取图片映射时发生错误: {e}")
            return None

    def get_movies_count_by_directory_path(self, directory_path: str) -> int:
        """
        根据目录路径获取指定目录下的影片数量

        Args:
            directory_path: 目录路径

        Returns:
            影片数量
        """
        try:
            # 查询文件路径以指定目录开头的影片数量
            count = self.db.query(Movie).filter(
                Movie.file_path.like(f"{directory_path}%")
            ).count()
            return count
        except Exception as e:
            logger.error(f"获取目录影片数量时发生错误: {e}")
            return 0

    def get_database_stats(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        try:
            stats = {
                "total_movies": self.db.query(Movie).count(),
                "total_tags": self.db.query(Tag).count(),
                "total_genres": self.db.query(Genre).count(),
                "total_actors": self.db.query(Actor).count(),
                "total_series": self.db.query(Series).count(),
                "total_images": self.db.query(VImage).count()
            }
            return stats
        except Exception as e:
            logger.error(f"获取数据库统计信息时发生错误: {e}")
            return {
                "total_movies": 0,
                "total_tags": 0,
                "total_genres": 0,
                "total_actors": 0,
                "total_series": 0,
                "total_images": 0
            }

    def detect_deleted_movies_by_directory(self, directory_id: int) -> List[Movie]:
        """
        检测指定目录下已被删除的影片（数据库中存在但文件系统中不存在）

        Args:
            directory_id: 目录ID

        Returns:
            已删除的影片列表
        """
        try:
            # 获取指定目录下的所有影片
            movies = self.db.query(Movie).filter(Movie.directory_id == directory_id).all()
            deleted_movies = []

            for movie in movies:
                if movie.file_path and not Path(movie.file_path).exists():
                    deleted_movies.append(movie)
                    logger.debug(f"检测到已删除的影片文件: {movie.file_path}")

            logger.info(f"目录 {directory_id} 检测到 {len(deleted_movies)} 个已删除的影片")
            return deleted_movies

        except Exception as e:
            logger.error(f"检测目录 {directory_id} 已删除影片时发生错误: {e}")
            return []

    def detect_all_deleted_movies(self) -> List[Movie]:
        """
        检测所有已被删除的影片（数据库中存在但文件系统中不存在）

        Returns:
            已删除的影片列表
        """
        try:
            # 获取所有影片
            movies = self.db.query(Movie).all()
            deleted_movies = []

            for movie in movies:
                if movie.file_path and not Path(movie.file_path).exists():
                    deleted_movies.append(movie)
                    logger.debug(f"检测到已删除的影片文件: {movie.file_path}")

            logger.info(f"全局检测到 {len(deleted_movies)} 个已删除的影片")
            return deleted_movies

        except Exception as e:
            logger.error(f"检测所有已删除影片时发生错误: {e}")
            return []

    def batch_delete_movies_by_ids(self, movie_ids: List[int]) -> Dict[str, Any]:
        """
        批量删除指定ID的影片记录

        Args:
            movie_ids: 要删除的影片ID列表

        Returns:
            删除结果统计
        """
        if not movie_ids:
            return {"deleted_count": 0, "failed_count": 0, "errors": []}

        deleted_count = 0
        failed_count = 0
        errors = []

        try:
            # 批量获取所有要删除的影片，避免N+1查询
            movies = self.db.query(Movie).filter(Movie.id.in_(movie_ids)).all()
            existing_movie_ids = {movie.id for movie in movies}

            # 记录不存在的影片ID
            for movie_id in movie_ids:
                if movie_id not in existing_movie_ids:
                    failed_count += 1
                    errors.append(f"影片 ID {movie_id} 不存在")

            if movies:
                try:
                    # 批量清理关联的虚拟图片记录
                    for movie in movies:
                        self._clear_movie_images(movie)

                    # 批量删除相关的收藏记录
                    from app.models.models import Favorite
                    deleted_favorites = self.db.query(Favorite).filter(
                        Favorite.movie_id.in_(existing_movie_ids)
                    ).delete(synchronize_session=False)
                    logger.debug(f"批量删除收藏记录: {deleted_favorites} 条")

                    # 批量删除影片记录
                    deleted_movies = self.db.query(Movie).filter(
                        Movie.id.in_(existing_movie_ids)
                    ).delete(synchronize_session=False)

                    deleted_count = deleted_movies
                    logger.info(f"批量删除影片记录: {deleted_count} 部影片")

                except Exception as e:
                    failed_count += len(existing_movie_ids)
                    error_msg = f"批量删除影片失败: {str(e)}"
                    errors.append(error_msg)
                    logger.error(error_msg)

            # 提交事务
            self.db.commit()

            logger.info(f"批量删除完成: 成功 {deleted_count} 个，失败 {failed_count} 个")
            return {
                "deleted_count": deleted_count,
                "failed_count": failed_count,
                "errors": errors
            }

        except Exception as e:
            self.db.rollback()
            logger.error(f"批量删除影片时发生错误: {e}")
            return {
                "deleted_count": 0,
                "failed_count": len(movie_ids),
                "errors": [f"批量删除失败: {str(e)}"]
            }

    def cleanup_deleted_movies_by_directory(self, directory_id: int) -> Dict[str, Any]:
        """
        清理指定目录下已删除的影片记录

        Args:
            directory_id: 目录ID

        Returns:
            清理结果统计
        """
        try:
            # 检测已删除的影片
            deleted_movies = self.detect_deleted_movies_by_directory(directory_id)

            if not deleted_movies:
                return {"deleted_count": 0, "failed_count": 0, "errors": []}

            # 批量删除
            movie_ids = [movie.id for movie in deleted_movies]
            result = self.batch_delete_movies_by_ids(movie_ids)

            logger.info(f"目录 {directory_id} 清理完成: 删除 {result['deleted_count']} 个影片记录")
            return result

        except Exception as e:
            logger.error(f"清理目录 {directory_id} 已删除影片时发生错误: {e}")
            return {
                "deleted_count": 0,
                "failed_count": 0,
                "errors": [f"清理失败: {str(e)}"]
            }

    def cleanup_all_deleted_movies(self) -> Dict[str, Any]:
        """
        清理所有已删除的影片记录

        Returns:
            清理结果统计
        """
        try:
            # 检测已删除的影片
            deleted_movies = self.detect_all_deleted_movies()

            if not deleted_movies:
                return {"deleted_count": 0, "failed_count": 0, "errors": []}

            # 批量删除
            movie_ids = [movie.id for movie in deleted_movies]
            result = self.batch_delete_movies_by_ids(movie_ids)

            logger.info(f"全局清理完成: 删除 {result['deleted_count']} 个影片记录")
            return result

        except Exception as e:
            logger.error(f"清理所有已删除影片时发生错误: {e}")
            return {
                "deleted_count": 0,
                "failed_count": 0,
                "errors": [f"清理失败: {str(e)}"]
            }
