"""
NFO 同步服务
用于将数据库中的影片信息同步回NFO文件
"""
import xml.etree.ElementTree as ET
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Union
import logging
import os
import threading
from sqlalchemy.orm import Session, joinedload
from app.models.models import Movie, Directory
from app.services.nfo_sync_progress_service import nfo_sync_progress_service

logger = logging.getLogger(__name__)


class NFOSyncService:
    """
    NFO 同步服务类 - 统一的NFO同步功能

    提供实例化的NFO同步服务，支持：
    - 单个影片NFO同步
    - 批量影片NFO同步
    - 已更新影片NFO同步
    - 统一的数据预加载策略
    - 自动清理更新标记
    - 完整的错误处理和日志记录
    """

    def __init__(self, db: Session):
        """
        初始化NFO同步服务

        Args:
            db: 数据库会话
        """
        self.db = db

    def sync_single_movie(self, movie_id: int) -> Dict[str, Any]:
        """
        同步单个影片的NFO文件

        Args:
            movie_id: 影片ID

        Returns:
            同步结果字典
        """
        try:
            # 使用统一的数据预加载策略获取影片
            movie = self._get_movie_with_preload(movie_id)
            if not movie:
                return {
                    'success': False,
                    'message': '影片不存在',
                    'updated_fields': []
                }

            # 执行NFO同步
            sync_result = self._sync_movie_to_nfo(movie)

            # 如果同步成功，清理更新标记
            if sync_result['success']:
                self._cleanup_update_mark(movie)
                # 添加影片标题到返回结果
                sync_result['movie_title'] = movie.title

            return sync_result

        except Exception as e:
            logger.error(f"同步单个影片NFO时发生错误 (ID: {movie_id}): {e}")
            return {
                'success': False,
                'message': f'同步失败: {str(e)}',
                'updated_fields': []
            }

    def sync_batch_movies(self, movie_ids: List[int]) -> Dict[str, Any]:
        """
        批量同步指定影片的NFO文件

        Args:
            movie_ids: 影片ID列表

        Returns:
            批量同步结果字典
        """
        try:
            # 使用统一的数据预加载策略获取影片列表
            movies = self._get_movies_with_preload(movie_ids)

            return self._batch_sync_movies(movies, movie_ids)

        except Exception as e:
            logger.error(f"批量同步NFO时发生错误: {e}")
            return {
                'success': False,
                'message': f'批量同步失败: {str(e)}',
                'total_count': len(movie_ids),
                'success_count': 0,
                'failed_count': len(movie_ids),
                'success_movie_ids': [],
                'failed_movie_ids': movie_ids,
                'errors': [f'批量同步失败: {str(e)}']
            }

    def sync_batch_movies_with_progress(self, movie_ids: List[int]) -> str:
        """
        批量同步指定影片的NFO文件（带进度追踪）

        Args:
            movie_ids: 影片ID列表

        Returns:
            任务ID
        """
        # 创建进度任务
        task_id = nfo_sync_progress_service.create_sync_task(movie_ids, "批量NFO同步")

        # 在后台线程中执行同步
        def sync_worker():
            try:
                self._sync_batch_with_progress(task_id, movie_ids)
            except Exception as e:
                logger.error(f"批量NFO同步任务失败: {e}")
                nfo_sync_progress_service.complete_sync_task(task_id, False, str(e))

        thread = threading.Thread(target=sync_worker, daemon=True)
        thread.start()

        return task_id

    def sync_updated_movies(self) -> Dict[str, Any]:
        """
        同步所有已更新影片的NFO文件

        Returns:
            批量同步结果字典
        """
        try:
            # 获取所有已更新的影片
            updated_movies = self._get_updated_movies_with_preload()

            if not updated_movies:
                return {
                    'success': True,
                    'message': '没有找到需要同步的已更新影片',
                    'total_count': 0,
                    'success_count': 0,
                    'failed_count': 0,
                    'success_movie_ids': [],
                    'failed_movie_ids': [],
                    'errors': []
                }

            movie_ids = [movie.id for movie in updated_movies]
            return self._batch_sync_movies(updated_movies, movie_ids)

        except Exception as e:
            logger.error(f"同步已更新影片NFO时发生错误: {e}")
            return {
                'success': False,
                'message': f'同步已更新影片失败: {str(e)}',
                'total_count': 0,
                'success_count': 0,
                'failed_count': 0,
                'success_movie_ids': [],
                'failed_movie_ids': [],
                'errors': [f'同步已更新影片失败: {str(e)}']
            }

    def sync_updated_movies_with_progress(self) -> str:
        """
        同步所有已更新影片的NFO文件（带进度追踪）

        Returns:
            任务ID
        """
        try:
            # 获取所有已更新的影片ID
            updated_movies = self._get_updated_movies_with_preload()

            if not updated_movies:
                # 创建一个空任务并立即完成
                task_id = nfo_sync_progress_service.create_sync_task([], "批量NFO同步")
                nfo_sync_progress_service.start_sync_task(task_id)
                nfo_sync_progress_service.complete_sync_task(task_id, True)
                return task_id

            movie_ids = [movie.id for movie in updated_movies]

            # 创建进度任务
            task_id = nfo_sync_progress_service.create_sync_task(movie_ids, "批量NFO同步")

            # 在后台线程中执行同步
            def sync_worker():
                try:
                    self._sync_batch_with_progress(task_id, movie_ids)
                except Exception as e:
                    logger.error(f"批量NFO同步任务失败: {e}")
                    nfo_sync_progress_service.complete_sync_task(task_id, False, str(e))

            thread = threading.Thread(target=sync_worker, daemon=True)
            thread.start()

            return task_id

        except Exception as e:
            logger.error(f"启动已更新影片NFO同步任务失败: {e}")
            # 创建一个失败的任务
            task_id = nfo_sync_progress_service.create_sync_task([], "批量NFO同步")
            nfo_sync_progress_service.start_sync_task(task_id)
            nfo_sync_progress_service.complete_sync_task(task_id, False, str(e))
            return task_id

    def _get_movie_with_preload(self, movie_id: int) -> Optional[Movie]:
        """
        使用统一的预加载策略获取影片

        Args:
            movie_id: 影片ID

        Returns:
            预加载了关系数据的影片对象或None
        """
        try:
            return self.db.query(Movie).options(
                joinedload(Movie.series),
                joinedload(Movie.tags),
                joinedload(Movie.genres),
                joinedload(Movie.actors),
                joinedload(Movie.directory)
            ).join(Directory).filter(
                Movie.id == movie_id,
                Directory.enabled == True
            ).first()
        except Exception as e:
            logger.error(f"获取影片时发生错误 (ID: {movie_id}): {e}")
            return None

    def _get_movies_with_preload(self, movie_ids: List[int]) -> List[Movie]:
        """
        使用统一的预加载策略批量获取影片

        Args:
            movie_ids: 影片ID列表

        Returns:
            预加载了关系数据的影片对象列表
        """
        try:
            return self.db.query(Movie).options(
                joinedload(Movie.series),
                joinedload(Movie.tags),
                joinedload(Movie.genres),
                joinedload(Movie.actors),
                joinedload(Movie.directory)
            ).join(Directory).filter(
                Movie.id.in_(movie_ids),
                Directory.enabled == True
            ).all()
        except Exception as e:
            logger.error(f"批量获取影片时发生错误: {e}")
            return []

    def _get_updated_movies_with_preload(self) -> List[Movie]:
        """
        获取所有已更新的影片，使用统一的预加载策略

        Returns:
            预加载了关系数据的已更新影片列表
        """
        try:
            return self.db.query(Movie).options(
                joinedload(Movie.series),
                joinedload(Movie.tags),
                joinedload(Movie.genres),
                joinedload(Movie.actors),
                joinedload(Movie.directory)
            ).join(Directory).filter(
                Movie.is_updated == True,
                Directory.enabled == True
            ).all()
        except Exception as e:
            logger.error(f"获取已更新影片时发生错误: {e}")
            return []

    def _batch_sync_movies(self, movies: List[Movie], movie_ids: List[int]) -> Dict[str, Any]:
        """
        执行批量影片NFO同步的核心逻辑

        Args:
            movies: 预加载的影片对象列表
            movie_ids: 原始影片ID列表（用于错误报告）

        Returns:
            批量同步结果字典
        """
        total_count = len(movie_ids)
        success_count = 0
        failed_count = 0
        success_movie_ids = []
        failed_movie_ids = []
        errors = []

        # 创建影片ID到影片对象的映射
        movie_map = {movie.id: movie for movie in movies}

        for movie_id in movie_ids:
            try:
                movie = movie_map.get(movie_id)
                if not movie:
                    failed_count += 1
                    failed_movie_ids.append(movie_id)
                    errors.append(f"影片ID {movie_id} 不存在或所在目录已停用")
                    continue

                # 执行NFO同步
                sync_result = self._sync_movie_to_nfo(movie)

                if sync_result['success']:
                    success_count += 1
                    success_movie_ids.append(movie_id)

                    # NFO同步成功后，清理该影片的已更新标记
                    self._cleanup_update_mark(movie)
                else:
                    failed_count += 1
                    failed_movie_ids.append(movie_id)
                    errors.append(f"影片《{movie.title}》: {sync_result['message']}")

            except Exception as e:
                failed_count += 1
                failed_movie_ids.append(movie_id)
                error_msg = f"影片ID {movie_id}: {str(e)}"
                errors.append(error_msg)
                logger.error(f"批量同步NFO时发生错误: {error_msg}")

        # 构建结果
        if success_count > 0:
            message = f"批量同步完成，成功 {success_count} 个，失败 {failed_count} 个"
        else:
            message = f"批量同步失败，共 {failed_count} 个影片同步失败"

        return {
            'success': success_count > 0,
            'message': message,
            'total_count': total_count,
            'success_count': success_count,
            'failed_count': failed_count,
            'success_movie_ids': success_movie_ids,
            'failed_movie_ids': failed_movie_ids,
            'errors': errors
        }

    def _cleanup_update_mark(self, movie: Movie) -> None:
        """
        清理影片的更新标记

        Args:
            movie: 影片对象
        """
        try:
            movie.is_updated = False
            movie.updated_at = None
            self.db.commit()
            logger.debug(f"NFO同步成功，已清理影片 {movie.title} 的更新标记")
        except Exception as e:
            logger.warning(f"清理影片 {movie.title} 的更新标记时发生错误: {e}")
            # 不影响主要的NFO同步结果
            self.db.rollback()

    def _sync_movie_to_nfo(self, movie: Movie) -> Dict[str, Any]:
        """
        将数据库中的影片信息同步到NFO文件（内部方法）

        Args:
            movie: 影片对象

        Returns:
            同步结果字典，包含成功状态、更新字段等信息
        """
        try:
            # 检查NFO文件路径
            nfo_path = NFOSyncService._get_nfo_path(movie)
            if not nfo_path:
                return {
                    'success': False,
                    'message': '无法确定NFO文件路径',
                    'updated_fields': []
                }

            # 检查文件权限
            permission_check = NFOSyncService._check_file_permissions(nfo_path)
            if not permission_check['success']:
                return permission_check

            # 读取现有NFO文件或创建新的
            if Path(nfo_path).exists():
                tree, root = NFOSyncService._load_existing_nfo(nfo_path)
                if tree is None:
                    return {
                        'success': False,
                        'message': 'NFO文件格式错误，无法解析',
                        'updated_fields': []
                    }
            else:
                tree, root = NFOSyncService._create_new_nfo()

            # 比较并更新字段
            updated_fields = NFOSyncService._update_nfo_fields(root, movie)

            # 如果有更新，保存文件
            if updated_fields:
                NFOSyncService._save_nfo_file(tree, nfo_path)
                return {
                    'success': True,
                    'message': f'成功同步 {len(updated_fields)} 个字段到NFO文件',
                    'updated_fields': updated_fields,
                    'nfo_path': nfo_path
                }
            else:
                return {
                    'success': True,
                    'message': 'NFO文件已是最新，无需更新',
                    'updated_fields': [],
                    'nfo_path': nfo_path
                }

        except Exception as e:
            logger.error(f"同步NFO文件时发生错误: {e}")
            return {
                'success': False,
                'message': f'同步失败: {str(e)}',
                'updated_fields': []
            }


    
    @staticmethod
    def _get_nfo_path(movie: Movie) -> Optional[str]:
        """
        获取NFO文件路径
        
        Args:
            movie: 影片对象
            
        Returns:
            NFO文件路径，如果无法确定则返回None
        """
        # 如果数据库中有NFO路径记录，直接使用
        if movie.nfo_path and Path(movie.nfo_path).exists():
            return movie.nfo_path
        
        # 根据影片文件路径推断NFO路径
        if movie.file_path:
            video_path = Path(movie.file_path)
            if video_path.exists():
                nfo_path = video_path.with_suffix('.nfo')
                return str(nfo_path)
        
        return None
    
    @staticmethod
    def _check_file_permissions(nfo_path: str) -> Dict[str, Any]:
        """
        检查文件权限
        
        Args:
            nfo_path: NFO文件路径
            
        Returns:
            权限检查结果
        """
        try:
            nfo_file = Path(nfo_path)
            
            # 验证路径安全性
            if not NFOSyncService._is_safe_path(nfo_path):
                return {
                    'success': False,
                    'message': '文件路径不安全',
                    'updated_fields': []
                }
            
            # 检查目录是否存在
            if not nfo_file.parent.exists():
                return {
                    'success': False,
                    'message': '目录不存在',
                    'updated_fields': []
                }
            
            # 检查写入权限
            if nfo_file.exists():
                if not os.access(nfo_path, os.W_OK):
                    return {
                        'success': False,
                        'message': '没有文件写入权限',
                        'updated_fields': []
                    }
            else:
                if not os.access(nfo_file.parent, os.W_OK):
                    return {
                        'success': False,
                        'message': '没有目录写入权限',
                        'updated_fields': []
                    }
            
            return {'success': True}
            
        except Exception as e:
            logger.error(f"检查文件权限时发生错误: {e}")
            return {
                'success': False,
                'message': f'权限检查失败: {str(e)}',
                'updated_fields': []
            }
    
    @staticmethod
    def _is_safe_path(file_path: str) -> bool:
        """
        验证文件路径安全性，防止路径遍历攻击
        
        Args:
            file_path: 文件路径
            
        Returns:
            路径是否安全
        """
        try:
            # 解析路径
            resolved_path = Path(file_path).resolve()
            
            # 检查是否包含危险的路径组件
            dangerous_patterns = ['..', '~', '$']
            path_str = str(resolved_path)
            
            for pattern in dangerous_patterns:
                if pattern in path_str:
                    return False
            
            # 检查文件扩展名
            if not path_str.lower().endswith('.nfo'):
                return False
            
            return True
            
        except Exception:
            return False
    
    @staticmethod
    def _load_existing_nfo(nfo_path: str) -> Tuple[Optional[ET.ElementTree], Optional[ET.Element]]:
        """
        加载现有的NFO文件
        
        Args:
            nfo_path: NFO文件路径
            
        Returns:
            (ElementTree对象, 根元素) 或 (None, None)
        """
        try:
            tree = ET.parse(nfo_path)
            root = tree.getroot()
            
            if root.tag != 'movie':
                logger.error(f"NFO文件格式错误，根元素不是 'movie': {nfo_path}")
                return None, None
            
            return tree, root
            
        except ET.ParseError as e:
            logger.error(f"NFO文件解析错误 {nfo_path}: {e}")
            return None, None
        except Exception as e:
            logger.error(f"加载NFO文件时发生错误 {nfo_path}: {e}")
            return None, None
    
    @staticmethod
    def _create_new_nfo() -> Tuple[ET.ElementTree, ET.Element]:
        """
        创建新的NFO文件结构
        
        Returns:
            (ElementTree对象, 根元素)
        """
        root = ET.Element('movie')
        tree = ET.ElementTree(root)
        return tree, root
    
    @staticmethod
    def _save_nfo_file(tree: ET.ElementTree, nfo_path: str) -> None:
        """
        保存NFO文件
        
        Args:
            tree: ElementTree对象
            nfo_path: NFO文件路径
        """
        # 格式化XML
        NFOSyncService._indent_xml(tree.getroot())
        
        # 保存文件
        tree.write(nfo_path, encoding='utf-8', xml_declaration=True)
        logger.info(f"NFO文件已保存: {nfo_path}")
    
    @staticmethod
    def _update_nfo_fields(root: ET.Element, movie: Movie) -> List[str]:
        """
        更新NFO文件字段

        Args:
            root: NFO文件根元素
            movie: 影片对象

        Returns:
            更新的字段列表
        """
        updated_fields = []

        # 基本文本字段映射
        text_fields = {
            'title': movie.title,
            'originaltitle': movie.original_title,
            'plot': movie.plot,
            'outline': movie.outline,
            'country': movie.country,
            'sorttitle': movie.sort_title,
            'trailer': movie.trailer,
            'num': movie.num
        }

        for xml_field, db_value in text_fields.items():
            if db_value is not None:
                if NFOSyncService._update_text_field(root, xml_field, str(db_value)):
                    updated_fields.append(xml_field)

        # 数值字段映射
        numeric_fields = {
            'year': movie.year,
            'runtime': movie.runtime,
            'rating': movie.rating,
            'criticrating': movie.critic_rating
        }

        for xml_field, db_value in numeric_fields.items():
            if db_value is not None:
                if NFOSyncService._update_text_field(root, xml_field, str(db_value)):
                    updated_fields.append(xml_field)

        # 布尔字段
        if movie.lock_data is not None:
            if NFOSyncService._update_text_field(root, 'lockdata', str(movie.lock_data).lower()):
                updated_fields.append('lockdata')

        # 日期字段
        date_fields = {
            'dateadded': movie.date_added,
            'premiered': movie.premiered,
            'releasedate': movie.release_date
        }

        for xml_field, db_value in date_fields.items():
            if db_value is not None:
                # 对于premiered和releasedate，只使用日期部分（YYYY-MM-DD）
                # 对于dateadded，保留完整的日期时间格式
                if xml_field in ['premiered', 'releasedate']:
                    date_str = db_value.strftime('%Y-%m-%d') if hasattr(db_value, 'strftime') else str(db_value)
                else:
                    date_str = db_value.strftime('%Y-%m-%d %H:%M:%S') if hasattr(db_value, 'strftime') else str(db_value)

                if NFOSyncService._update_text_field(root, xml_field, date_str):
                    updated_fields.append(xml_field)

        # 更新分类
        if NFOSyncService._update_list_field(root, 'genre', [genre.name for genre in movie.genres]):
            updated_fields.append('genres')

        # 更新标签
        if NFOSyncService._update_list_field(root, 'tag', [tag.name for tag in movie.tags]):
            updated_fields.append('tags')

        # 更新演员
        if NFOSyncService._update_actors_field(root, movie.actors):
            updated_fields.append('actors')

        # 更新系列
        if movie.series:
            if NFOSyncService._update_series_field(root, movie.series.name):
                updated_fields.append('series')

        return updated_fields

    @staticmethod
    def _update_text_field(root: ET.Element, field_name: str, new_value: str) -> bool:
        """
        更新文本字段

        Args:
            root: NFO文件根元素
            field_name: 字段名
            new_value: 新值

        Returns:
            是否有更新
        """
        element = root.find(field_name)

        if element is None:
            # 字段不存在，创建新字段
            element = ET.SubElement(root, field_name)
            element.text = new_value
            return True
        else:
            # 字段存在，检查是否需要更新
            current_value = element.text or ''
            if current_value.strip() != new_value.strip():
                element.text = new_value
                return True

        return False

    @staticmethod
    def _update_list_field(root: ET.Element, field_name: str, new_values: List[str]) -> bool:
        """
        更新列表字段（如分类、标签）

        Args:
            root: NFO文件根元素
            field_name: 字段名
            new_values: 新值列表

        Returns:
            是否有更新
        """
        # 获取现有值
        existing_elements = root.findall(field_name)
        existing_values = [elem.text.strip() for elem in existing_elements if elem.text]

        # 比较值
        new_values_set = set(new_values)
        existing_values_set = set(existing_values)

        if new_values_set == existing_values_set:
            return False

        # 删除所有现有元素
        for elem in existing_elements:
            root.remove(elem)

        # 添加新元素
        for value in new_values:
            if value.strip():
                elem = ET.SubElement(root, field_name)
                elem.text = value.strip()

        return True

    @staticmethod
    def _update_actors_field(root: ET.Element, actors) -> bool:
        """
        更新演员字段

        Args:
            root: NFO文件根元素
            actors: 演员列表

        Returns:
            是否有更新
        """
        # 获取现有演员
        existing_actors = root.findall('actor')
        existing_actor_data = []

        for actor_elem in existing_actors:
            name_elem = actor_elem.find('name')
            role_elem = actor_elem.find('role')
            type_elem = actor_elem.find('type')

            if name_elem is not None and name_elem.text:
                existing_actor_data.append({
                    'name': name_elem.text.strip(),
                    'role': role_elem.text.strip() if role_elem is not None and role_elem.text else '',
                    'type': type_elem.text.strip() if type_elem is not None and type_elem.text else ''
                })

        # 构建新演员数据
        new_actor_data = []
        for actor in actors:
            new_actor_data.append({
                'name': actor.name,
                'role': actor.role or '',
                'type': actor.actor_type or 'Actor'
            })

        # 比较演员数据
        if NFOSyncService._compare_actor_lists(existing_actor_data, new_actor_data):
            return False

        # 删除所有现有演员元素
        for actor_elem in existing_actors:
            root.remove(actor_elem)

        # 添加新演员元素
        for actor_data in new_actor_data:
            actor_elem = ET.SubElement(root, 'actor')

            # 使用 'name' 标签，这是演员字段的标准格式
            name_elem = ET.SubElement(actor_elem, 'name')
            name_elem.text = actor_data['name']

            if actor_data['role']:
                role_elem = ET.SubElement(actor_elem, 'role')
                role_elem.text = actor_data['role']

            if actor_data['type']:
                type_elem = ET.SubElement(actor_elem, 'type')
                type_elem.text = actor_data['type']

        return True

    @staticmethod
    def _update_series_field(root: ET.Element, series_name: str) -> bool:
        """
        更新系列字段

        Args:
            root: NFO文件根元素
            series_name: 系列名称

        Returns:
            是否有更新
        """
        set_elem = root.find('set')

        if set_elem is None:
            # 系列元素不存在，创建新的
            set_elem = ET.SubElement(root, 'set')
            name_elem = ET.SubElement(set_elem, 'name')
            name_elem.text = series_name
            return True
        else:
            # 系列元素存在，检查name子元素
            name_elem = set_elem.find('name')

            if name_elem is None:
                # 没有name子元素，创建一个
                # 先清理set元素的直接文本内容（如果有的话）
                set_elem.text = None
                name_elem = ET.SubElement(set_elem, 'name')
                name_elem.text = series_name
                return True
            else:
                # 有name子元素，检查内容
                current_name = name_elem.text.strip() if name_elem.text else ''

                if current_name != series_name.strip():
                    name_elem.text = series_name
                    return True

        return False

    @staticmethod
    def _compare_actor_lists(existing: List[Dict], new: List[Dict]) -> bool:
        """
        比较演员列表是否相同

        Args:
            existing: 现有演员列表
            new: 新演员列表

        Returns:
            是否相同
        """
        if len(existing) != len(new):
            return False

        # 按名称排序后比较
        existing_sorted = sorted(existing, key=lambda x: x['name'])
        new_sorted = sorted(new, key=lambda x: x['name'])

        for i in range(len(existing_sorted)):
            if (existing_sorted[i]['name'] != new_sorted[i]['name'] or
                existing_sorted[i]['role'] != new_sorted[i]['role'] or
                existing_sorted[i]['type'] != new_sorted[i]['type']):
                return False

        return True

    @staticmethod
    def _indent_xml(elem: ET.Element, level: int = 0) -> None:
        """
        格式化XML缩进

        Args:
            elem: XML元素
            level: 缩进级别
        """
        i = "\n" + level * "  "
        if len(elem):
            if not elem.text or not elem.text.strip():
                elem.text = i + "  "
            if not elem.tail or not elem.tail.strip():
                elem.tail = i
            for elem in elem:
                NFOSyncService._indent_xml(elem, level + 1)
            if not elem.tail or not elem.tail.strip():
                elem.tail = i
        else:
            if level and (not elem.tail or not elem.tail.strip()):
                elem.tail = i

    def _sync_batch_with_progress(self, task_id: str, movie_ids: List[int]) -> None:
        """
        带进度追踪的批量同步方法

        Args:
            task_id: 任务ID
            movie_ids: 影片ID列表
        """
        try:
            # 开始任务
            nfo_sync_progress_service.start_sync_task(task_id)

            # 获取影片数据
            movies = self._get_movies_with_preload(movie_ids)
            movie_map = {movie.id: movie for movie in movies}

            # 更新任务状态
            nfo_sync_progress_service.update_sync_progress(
                task_id,
                current_movie_title="正在准备同步...",
                current_index=0
            )

            success_count = 0
            failed_count = 0
            success_movie_ids = []
            failed_movie_ids = []
            errors = []

            # 逐个处理影片
            for index, movie_id in enumerate(movie_ids):
                # 检查是否请求取消
                if nfo_sync_progress_service.is_cancel_requested(task_id):
                    nfo_sync_progress_service.complete_sync_task(task_id, False, "用户取消操作")
                    return

                try:
                    movie = movie_map.get(movie_id)
                    if not movie:
                        failed_count += 1
                        failed_movie_ids.append(movie_id)
                        errors.append(f"影片ID {movie_id} 不存在或所在目录已停用")
                        continue

                    # 更新当前处理的影片
                    nfo_sync_progress_service.update_sync_progress(
                        task_id,
                        current_movie_title=f"正在同步: {movie.title}",
                        current_movie_id=movie_id,
                        current_index=index + 1
                    )

                    # 执行NFO同步
                    sync_result = self._sync_movie_to_nfo(movie)

                    if sync_result['success']:
                        success_count += 1
                        success_movie_ids.append(movie_id)

                        # NFO同步成功后，清理该影片的已更新标记
                        self._cleanup_update_mark(movie)
                    else:
                        failed_count += 1
                        failed_movie_ids.append(movie_id)
                        errors.append(f"影片《{movie.title}》: {sync_result['message']}")

                    # 更新统计信息
                    nfo_sync_progress_service.update_sync_progress(
                        task_id,
                        success_count=success_count,
                        failed_count=failed_count,
                        success_movie_ids=success_movie_ids,
                        failed_movie_ids=failed_movie_ids,
                        errors=errors
                    )

                except Exception as e:
                    failed_count += 1
                    failed_movie_ids.append(movie_id)
                    error_msg = f"影片ID {movie_id}: {str(e)}"
                    errors.append(error_msg)
                    logger.error(f"批量同步NFO时发生错误: {error_msg}")

                    # 更新错误信息
                    nfo_sync_progress_service.update_sync_progress(
                        task_id,
                        failed_count=failed_count,
                        failed_movie_ids=failed_movie_ids,
                        errors=errors
                    )

            # 完成任务
            nfo_sync_progress_service.update_sync_progress(
                task_id,
                current_movie_title="同步完成",
                current_index=len(movie_ids)
            )

            nfo_sync_progress_service.complete_sync_task(task_id, True)

        except Exception as e:
            logger.error(f"批量NFO同步任务执行失败: {e}")
            nfo_sync_progress_service.complete_sync_task(task_id, False, str(e))
