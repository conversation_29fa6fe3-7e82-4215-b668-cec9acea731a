{% extends "base.html" %}

{% block title %}系列管理 - 媒体管理器{% endblock %}

{% block page_header %}
<!-- 页面标题区域 -->
<div class="bg-gradient-to-r from-primary/10 to-secondary/10 border-b border-base-300">
    <div class="container mx-auto px-4 py-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
                <h1 class="text-3xl font-bold text-base-content flex items-center gap-3">
                    <i class="bi bi-collection-play text-primary" style="font-size: 2rem;" aria-label="系列管理图标"></i>
                    系列管理
                </h1>
                <p class="text-base-content/70 mt-2">管理影片系列，组织您的媒体库</p>
            </div>
            <div class="flex items-center gap-3">
                <button type="button" class="btn btn-primary btn-sm" id="add-series-btn">
                    <i class="bi bi-plus" aria-label="添加图标"></i>
                    添加系列
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- 搜索和操作区域 -->
<div class="card bg-base-100 shadow-lg border border-base-300 mb-4 md:mb-6">
    <div class="card-body p-3 md:p-6">
        <div class="flex flex-col md:flex-row gap-3 md:gap-4 items-stretch md:items-end">
            <!-- 搜索输入框 -->
            <div class="form-control flex-1">
                <label class="label py-1 md:py-2">
                    <span class="label-text font-medium text-sm md:text-base">搜索系列</span>
                </label>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="bi bi-search text-base-content/40" aria-label="搜索图标"></i>
                    </div>
                    <input type="text" class="input input-bordered w-full pl-10 pr-10 input-sm md:input-md" id="search-input" placeholder="搜索系列名称或描述...">
                    <button class="absolute inset-y-0 right-0 pr-2 flex items-center btn btn-ghost btn-xs btn-circle hidden"
                            type="button" id="clear-search-btn" title="清除搜索">
                        <i class="bi bi-x" style="font-size: 0.75rem;" aria-label="清除搜索图标"></i>
                    </button>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="form-control">
                <label class="label py-1 md:py-2 md:opacity-0">
                    <span class="label-text text-sm md:text-base">　</span>
                </label>
                <div class="flex gap-2 flex-wrap">
                    <button type="button" class="btn btn-outline btn-sm md:btn-md flex-1 md:flex-none" id="refresh-btn">
                        <i class="bi bi-arrow-clockwise md:mr-2" aria-label="刷新图标"></i>
                        刷新
                    </button>
                    <button type="button" class="btn btn-warning btn-sm md:btn-md flex-1 md:flex-none hidden" id="batch-merge-btn">
                        <i class="bi bi-union md:mr-2" aria-label="批量合并图标"></i>
                        批量合并
                    </button>
                    <button type="button" class="btn btn-error btn-sm md:btn-md flex-1 md:flex-none hidden" id="batch-delete-btn">
                        <i class="bi bi-trash md:mr-2" aria-label="批量删除图标"></i>
                        批量删除
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 统计信息 -->
<div class="stats shadow mb-6 w-full">
    <div class="stat">
        <div class="stat-figure text-primary">
            <i class="bi bi-collection-play text-primary" style="font-size: 2rem;" aria-label="总系列图标"></i>
        </div>
        <div class="stat-title">总系列数</div>
        <div class="stat-value text-primary" id="total-series">0</div>
        <div class="stat-desc">系统中的系列总数</div>
    </div>

    <div class="stat">
        <div class="stat-figure text-secondary">
            <i class="bi bi-check-circle text-secondary" style="font-size: 2rem;" aria-label="已使用系列图标"></i>
        </div>
        <div class="stat-title">已使用系列</div>
        <div class="stat-value text-secondary" id="used-series">0</div>
        <div class="stat-desc">已关联影片的系列</div>
    </div>

    <div class="stat">
        <div class="stat-figure text-accent">
            <i class="bi bi-circle text-accent" style="font-size: 2rem;" aria-label="未使用系列图标"></i>
        </div>
        <div class="stat-title">未使用系列</div>
        <div class="stat-value text-accent" id="unused-series">0</div>
        <div class="stat-desc">未关联影片的系列</div>
    </div>

    <div class="stat">
        <div class="stat-figure text-info">
            <i class="bi bi-info-circle text-info" style="font-size: 2rem;" aria-label="选中系列图标"></i>
        </div>
        <div class="stat-title">选中系列</div>
        <div class="stat-value text-info" id="selected-series">0</div>
        <div class="stat-desc">当前选中的系列数</div>
    </div>
</div>

<!-- 系列表格 -->
<div id="table-view" class="card bg-base-100 shadow-lg border border-base-300">
    <div class="card-body p-0">
        <!-- 表格头部 -->
        <div class="flex items-center justify-between p-6 border-b border-base-300">
            <h3 class="text-xl font-semibold">系列列表</h3>
        </div>

        <!-- 表格内容 -->
        <div class="overflow-x-auto">
            <table class="table table-zebra">
                <thead>
                    <tr>
                        <th class="w-12">
                            <input type="checkbox" class="checkbox checkbox-primary" id="table-select-all">
                        </th>
                        <th>系列名称</th>
                        <th>描述</th>
                        <th>关联影片数</th>
                        <th>创建时间</th>
                        <th class="w-32">操作</th>
                    </tr>
                </thead>
                <tbody id="series-table-body">
                    <!-- 系列数据将在这里动态加载 -->
                </tbody>
            </table>
        </div>

        <!-- 表格底部 -->
        <div class="flex items-center justify-between p-6 border-t border-base-300">
            <div class="text-sm text-base-content/60" id="series-count-info">
                共 0 个系列
            </div>
            <div id="pagination-container">
                <!-- 分页将在这里生成 -->
            </div>
        </div>
    </div>
</div>

<!-- 加载状态 -->
<div class="flex flex-col items-center justify-center py-16 hidden" id="loading-state">
    <span class="loading loading-spinner loading-lg mb-4"></span>
    <p class="text-base-content/60">加载系列数据...</p>
</div>

<!-- 空状态 -->
<div class="flex flex-col items-center justify-center py-16 hidden" id="empty-state">
    <div class="bg-base-200 rounded-full p-6 mb-6">
        <i class="bi bi-collection-play text-base-content/40" style="font-size: 4rem;" aria-label="空系列图标"></i>
    </div>
    <h3 class="text-2xl font-bold mb-2 text-base-content">暂无系列</h3>
    <p class="text-base-content/60 mb-6 text-center max-w-md">您还没有创建任何系列，点击下方按钮创建第一个系列。</p>
    <button type="button" class="btn btn-primary btn-wide" id="add-first-series-btn">
        <i class="bi bi-plus mr-2" aria-label="添加图标"></i>
        创建第一个系列
    </button>
</div>

<!-- 添加/编辑系列模态框 -->
<dialog id="series-modal" class="modal bg-base-100/80">
    <div class="modal-box w-11/12 max-w-lg bg-base-100/80 backdrop-blur-sm">
        <form method="dialog">
            <button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2">✕</button>
        </form>
        <h3 class="font-bold text-xl mb-6" id="modal-title">添加系列</h3>

        <form id="series-form" class="space-y-6">
            <input type="hidden" id="series-id">

            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">系列名称 <span class="text-error">*</span></span>
                </label>
                <div class="join w-full">
                    <input type="text" class="input input-bordered join-item flex-1" id="series-name" name="name" placeholder="输入系列名称" required>
                    <button type="button" class="btn btn-outline join-item translation-btn" data-field="name" data-target="series-name">
                        <span class="loading loading-spinner loading-xs mr-1 hidden"></span>
                        <i class="bi bi-translate mr-1"></i>
                        翻译
                    </button>
                </div>
                <div class="label">
                    <span class="label-text-alt text-error hidden" id="series-name-error"></span>
                </div>
            </div>

            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">描述</span>
                </label>
                <div class="relative">
                    <textarea class="textarea textarea-bordered w-full" id="series-description" name="description" rows="3" placeholder="输入系列描述（可选）"></textarea>
                    <button type="button" class="btn btn-outline btn-sm absolute top-2 right-2 translation-btn" data-field="description" data-target="series-description">
                        <span class="loading loading-spinner loading-xs mr-1 hidden"></span>
                        <i class="bi bi-translate mr-1"></i>
                        翻译
                    </button>
                </div>
                <div class="label">
                    <span class="label-text-alt text-error hidden" id="series-description-error"></span>
                </div>
            </div>

            <!-- 批量翻译按钮 -->
            <div class="flex justify-end">
                <button type="button" class="btn btn-primary btn-sm batch-translation-btn">
                    <span class="loading loading-spinner loading-sm mr-2 hidden"></span>
                    <i class="bi bi-translate mr-2"></i>
                    批量翻译
                </button>
            </div>

            <div class="modal-action">
                <button type="button" class="btn btn-outline" onclick="document.getElementById('series-modal').close()">取消</button>
                <button type="submit" class="btn btn-primary" id="series-submit-btn">
                    <span class="loading loading-spinner loading-sm mr-2 hidden"></span>
                    <i class="bi bi-check mr-2" aria-label="保存图标"></i>
                    保存
                </button>
            </div>
        </form>
    </div>
</dialog>

<!-- 删除确认模态框 -->
<dialog id="delete-series-modal" class="modal bg-base-100/80">
    <div class="modal-box w-11/12 max-w-lg bg-base-100/80 backdrop-blur-sm">
        <form method="dialog">
            <button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2">✕</button>
        </form>

        <h3 class="font-bold text-xl mb-4">确认删除系列</h3>
        <p class="mb-4" id="delete-series-message">您确定要删除这个系列吗？</p>

        <div class="form-control mb-6">
            <label class="label cursor-pointer justify-start gap-3">
                <input type="checkbox" class="checkbox checkbox-warning" id="force-delete-series-checkbox">
                <span class="label-text">强制删除（忽略关联关系）</span>
            </label>
            <div class="label">
                <span class="label-text-alt">勾选此项将强制删除系列，即使它被影片使用</span>
            </div>
        </div>

        <div class="modal-action">
            <button type="button" class="btn btn-outline" onclick="document.getElementById('delete-series-modal').close()">取消</button>
            <button type="button" class="btn btn-error" id="confirm-delete-series-btn">
                <span class="loading loading-spinner loading-sm mr-2 hidden"></span>
                删除
            </button>
        </div>
    </div>
</dialog>

<!-- 批量删除确认模态框 -->
<dialog id="batch-delete-series-modal" class="modal bg-base-100/80">
    <div class="modal-box w-11/12 max-w-lg bg-base-100/80 backdrop-blur-sm">
        <form method="dialog">
            <button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2">✕</button>
        </form>

        <h3 class="font-bold text-xl mb-4">批量删除确认</h3>
        <p class="mb-4" id="batch-delete-series-message">您确定要删除选中的系列吗？</p>

        <div class="form-control mb-6">
            <label class="label cursor-pointer justify-start gap-3">
                <input type="checkbox" class="checkbox checkbox-warning" id="force-batch-delete-series-checkbox">
                <span class="label-text">强制删除（忽略关联关系）</span>
            </label>
            <div class="label">
                <span class="label-text-alt">勾选此项将强制删除系列，即使它们被影片使用</span>
            </div>
        </div>

        <div class="modal-action">
            <button type="button" class="btn btn-outline" onclick="document.getElementById('batch-delete-series-modal').close()">取消</button>
            <button type="button" class="btn btn-error" id="confirm-batch-delete-btn">
                <span class="loading loading-spinner loading-sm mr-2 hidden"></span>
                删除全部
            </button>
        </div>
    </div>
</dialog>

<!-- 批量合并对话框 -->
<dialog id="batch-merge-series-modal" class="modal">
    <div class="modal-box w-11/12 max-w-2xl">
        <h3 class="font-bold text-lg mb-4">
            <i class="bi bi-union mr-2 text-warning"></i>
            批量合并系列
        </h3>

        <div class="space-y-4">
            <!-- 选中项目信息 -->
            <div class="alert alert-info">
                <i class="bi bi-info-circle"></i>
                <div>
                    <div class="font-semibold">即将合并 <span id="merge-selected-count">0</span> 个系列</div>
                    <div class="text-sm opacity-75" id="merge-selected-names">未选择任何系列</div>
                </div>
            </div>

            <!-- 目标名称输入 -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-semibold">
                        <i class="bi bi-collection mr-1"></i>
                        合并后的系列名称 <span class="text-error">*</span>
                    </span>
                </label>
                <input type="text" class="input input-bordered" id="merge-target-name" placeholder="请输入合并后的系列名称">
                <label class="label">
                    <span class="label-text-alt text-base-content/60">将创建新系列或使用现有系列作为合并目标</span>
                </label>
            </div>

            <!-- 目标描述输入 -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-semibold">
                        <i class="bi bi-text-paragraph mr-1"></i>
                        系列描述（可选）
                    </span>
                </label>
                <textarea class="textarea textarea-bordered" id="merge-target-description" placeholder="请输入系列描述（可选）" rows="3"></textarea>
            </div>

            <!-- 冲突警告 -->
            <div class="alert alert-warning hidden" id="merge-conflict-warning">
                <i class="bi bi-exclamation-triangle"></i>
                <div>
                    <div class="font-semibold">检测到名称冲突</div>
                    <div class="text-sm" id="merge-conflict-message"></div>
                </div>
            </div>
        </div>

        <div class="modal-action">
            <button type="button" class="btn btn-outline" onclick="document.getElementById('batch-merge-series-modal').close()">取消</button>
            <button type="button" class="btn btn-warning" id="confirm-batch-merge-btn">
                <span class="loading loading-spinner loading-sm mr-2 hidden"></span>
                <i class="bi bi-union mr-2"></i>
                确认合并
            </button>
        </div>
    </div>
</dialog>

<!-- 重命名冲突对话框 -->
<dialog id="rename-conflict-modal" class="modal">
    <div class="modal-box w-11/12 max-w-lg">
        <h3 class="font-bold text-lg mb-4">
            <i class="bi bi-exclamation-triangle mr-2 text-warning"></i>
            重命名冲突
        </h3>

        <div class="space-y-4">
            <div class="alert alert-warning">
                <i class="bi bi-exclamation-triangle"></i>
                <div>
                    <div class="font-semibold">名称已存在</div>
                    <div class="text-sm" id="rename-conflict-details">检测到名称冲突</div>
                </div>
            </div>

            <div class="text-sm text-base-content/70">
                您可以选择：
                <ul class="list-disc list-inside mt-2 space-y-1">
                    <li>取消重命名操作</li>
                    <li>将当前系列与现有系列合并</li>
                </ul>
            </div>
        </div>

        <div class="modal-action">
            <button type="button" class="btn btn-outline" id="cancel-rename-btn">取消重命名</button>
            <button type="button" class="btn btn-warning" id="confirm-rename-merge-btn">
                <span class="loading loading-spinner loading-sm mr-2 hidden"></span>
                <i class="bi bi-union mr-2"></i>
                合并系列
            </button>
        </div>
    </div>
</dialog>
{% endblock %}

{% block extra_js %}
<script src="/static/js/translation-helper.js?v={{ app_version }}"></script>
<script src="/static/js/data-manager.js?v={{ app_version }}"></script>
{% endblock %}
