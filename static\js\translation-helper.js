/**
 * 翻译助手工具类
 * 处理翻译请求、进度显示、错误处理等
 */

class TranslationHelper {
    constructor() {
        this.isTranslating = false;
        this.translationQueue = [];
        this.maxConcurrentRequests = 3;
        this.activeRequests = 0;
    }

    /**
     * 检查翻译服务是否可用
     */
    async checkTranslationAvailable() {
        try {
            const result = await window.api.getTranslationStatus();

            if (result.success && result.data) {
                return result.data.enabled && result.data.configured;
            }
            return false;
        } catch (error) {
            console.error('检查翻译服务状态失败:', error);
            return false;
        }
    }

    /**
     * 翻译单个文本
     */
    async translateText(text, fieldName = '') {
        try {
            const result = await window.api.translateText(text, fieldName);

            if (result.success && result.data) {
                return {
                    success: result.data.success,
                    originalText: result.data.original_text,
                    translatedText: result.data.translated_text,
                    error: result.data.error
                };
            } else {
                throw new Error(result.message || '翻译请求失败');
            }
        } catch (error) {
            console.error('翻译文本失败:', error);
            return {
                success: false,
                originalText: text,
                translatedText: text,
                error: error.message || '翻译服务异常'
            };
        }
    }

    /**
     * 批量翻译多个字段
     */
    async translateBatch(fields) {
        try {
            const result = await window.api.translateBatch(fields);

            if (result.success && result.data) {
                return {
                    success: true,
                    results: result.data.results,
                    totalCount: result.data.total_count,
                    successCount: result.data.success_count,
                    failedCount: result.data.failed_count
                };
            } else {
                throw new Error(result.message || '批量翻译请求失败');
            }
        } catch (error) {
            console.error('批量翻译失败:', error);
            return {
                success: false,
                error: error.message || '批量翻译服务异常',
                results: {}
            };
        }
    }

    /**
     * 为表单字段添加翻译按钮
     */
    addTranslationButton(inputElement, options = {}) {
        const {
            fieldName = '',
            buttonText = '翻译',
            buttonClass = 'btn btn-outline btn-sm',
            position = 'after', // 'after' | 'before' | 'append'
            onTranslationStart = null,
            onTranslationComplete = null,
            onTranslationError = null
        } = options;

        // 检查是否已经添加了翻译按钮
        const existingButton = inputElement.parentElement.querySelector('.translation-btn');
        if (existingButton) {
            return existingButton;
        }

        // 创建翻译按钮
        const button = document.createElement('button');
        button.type = 'button';
        button.className = `${buttonClass} translation-btn`;
        button.innerHTML = `
            <span class="loading loading-spinner loading-xs mr-1 hidden"></span>
            <i class="bi bi-translate mr-1"></i>
            ${buttonText}
        `;

        // 添加点击事件
        button.addEventListener('click', async () => {
            await this.handleTranslationClick(inputElement, button, {
                fieldName,
                onTranslationStart,
                onTranslationComplete,
                onTranslationError
            });
        });

        // 根据位置插入按钮
        this.insertButton(inputElement, button, position);

        return button;
    }

    /**
     * 处理翻译按钮点击事件
     */
    async handleTranslationClick(inputElement, button, options = {}) {
        const {
            fieldName = '',
            onTranslationStart = null,
            onTranslationComplete = null,
            onTranslationError = null
        } = options;

        try {
            // 检查翻译服务是否可用
            const isAvailable = await this.checkTranslationAvailable();
            if (!isAvailable) {
                throw new Error('翻译服务不可用，请检查配置');
            }

            // 获取要翻译的文本
            const text = inputElement.value?.trim();
            if (!text) {
                throw new Error('请先输入要翻译的内容');
            }

            // 开始翻译
            this.setButtonLoading(button, true);
            if (onTranslationStart) {
                onTranslationStart(text, fieldName);
            }

            // 调用翻译API
            const result = await this.translateText(text, fieldName);

            if (result.success) {
                // 翻译成功，更新输入框
                inputElement.value = result.translatedText;
                
                // 触发输入事件以便其他组件感知变化
                inputElement.dispatchEvent(new Event('input', { bubbles: true }));
                inputElement.dispatchEvent(new Event('change', { bubbles: true }));

                if (onTranslationComplete) {
                    onTranslationComplete(result, fieldName);
                }

                // 显示成功提示
                this.showTranslationToast(`${fieldName || '文本'}翻译成功`, 'success');
            } else {
                throw new Error(result.error || '翻译失败');
            }

        } catch (error) {
            console.error('翻译失败:', error);
            
            if (onTranslationError) {
                onTranslationError(error, fieldName);
            }

            // 显示错误提示
            this.showTranslationToast(error.message || '翻译失败', 'error');
        } finally {
            this.setButtonLoading(button, false);
        }
    }

    /**
     * 批量为表单添加翻译按钮
     */
    addBatchTranslationButtons(formElement, fieldConfigs = []) {
        const buttons = [];
        
        fieldConfigs.forEach(config => {
            const { selector, ...options } = config;
            const inputElement = formElement.querySelector(selector);
            
            if (inputElement) {
                const button = this.addTranslationButton(inputElement, options);
                buttons.push(button);
            }
        });

        return buttons;
    }

    /**
     * 添加批量翻译按钮
     */
    addBatchTranslationButton(formElement, fieldConfigs = [], options = {}) {
        const {
            buttonText = '批量翻译',
            buttonClass = 'btn btn-primary btn-sm',
            container = null,
            onBatchStart = null,
            onBatchComplete = null,
            onBatchError = null
        } = options;

        // 创建批量翻译按钮
        const button = document.createElement('button');
        button.type = 'button';
        button.className = `${buttonClass} batch-translation-btn`;
        button.innerHTML = `
            <span class="loading loading-spinner loading-sm mr-2 hidden"></span>
            <i class="bi bi-translate mr-2"></i>
            ${buttonText}
        `;

        // 添加点击事件
        button.addEventListener('click', async () => {
            await this.handleBatchTranslation(formElement, fieldConfigs, button, {
                onBatchStart,
                onBatchComplete,
                onBatchError
            });
        });

        // 插入按钮
        if (container) {
            container.appendChild(button);
        } else {
            formElement.appendChild(button);
        }

        return button;
    }

    /**
     * 处理批量翻译
     */
    async handleBatchTranslation(formElement, fieldConfigs, button, options = {}) {
        const {
            onBatchStart = null,
            onBatchComplete = null,
            onBatchError = null
        } = options;

        try {
            // 检查翻译服务是否可用
            const isAvailable = await this.checkTranslationAvailable();
            if (!isAvailable) {
                throw new Error('翻译服务不可用，请检查配置');
            }

            // 收集要翻译的字段
            const fields = {};
            fieldConfigs.forEach(config => {
                const { selector, fieldName } = config;
                const inputElement = formElement.querySelector(selector);
                
                if (inputElement && inputElement.value?.trim()) {
                    fields[fieldName || selector] = inputElement.value.trim();
                }
            });

            if (Object.keys(fields).length === 0) {
                throw new Error('没有找到需要翻译的内容');
            }

            // 开始批量翻译
            this.setButtonLoading(button, true);
            if (onBatchStart) {
                onBatchStart(fields);
            }

            // 调用批量翻译API
            const result = await this.translateBatch(fields);

            if (result.success) {
                // 更新表单字段
                fieldConfigs.forEach(config => {
                    const { selector, fieldName } = config;
                    const inputElement = formElement.querySelector(selector);
                    const key = fieldName || selector;
                    
                    if (inputElement && result.results[key] && result.results[key].success) {
                        inputElement.value = result.results[key].translated_text;
                        
                        // 触发输入事件
                        inputElement.dispatchEvent(new Event('input', { bubbles: true }));
                        inputElement.dispatchEvent(new Event('change', { bubbles: true }));
                    }
                });

                if (onBatchComplete) {
                    onBatchComplete(result);
                }

                // 显示成功提示
                this.showTranslationToast(
                    `批量翻译完成，成功 ${result.successCount} 个，失败 ${result.failedCount} 个`, 
                    'success'
                );
            } else {
                throw new Error(result.error || '批量翻译失败');
            }

        } catch (error) {
            console.error('批量翻译失败:', error);
            
            if (onBatchError) {
                onBatchError(error);
            }

            // 显示错误提示
            this.showTranslationToast(error.message || '批量翻译失败', 'error');
        } finally {
            this.setButtonLoading(button, false);
        }
    }

    /**
     * 插入翻译按钮到指定位置
     */
    insertButton(inputElement, button, position) {
        switch (position) {
            case 'before':
                inputElement.parentElement.insertBefore(button, inputElement);
                break;
            case 'append':
                inputElement.parentElement.appendChild(button);
                break;
            case 'after':
            default:
                if (inputElement.nextSibling) {
                    inputElement.parentElement.insertBefore(button, inputElement.nextSibling);
                } else {
                    inputElement.parentElement.appendChild(button);
                }
                break;
        }
    }

    /**
     * 设置按钮加载状态
     */
    setButtonLoading(button, isLoading) {
        const spinner = button.querySelector('.loading');
        const icon = button.querySelector('.bi-translate');

        if (isLoading) {
            button.disabled = true;
            if (spinner) spinner.classList.remove('hidden');
            if (icon) icon.classList.add('hidden');
        } else {
            button.disabled = false;
            if (spinner) spinner.classList.add('hidden');
            if (icon) icon.classList.remove('hidden');
        }
    }

    /**
     * 显示翻译提示消息
     */
    showTranslationToast(message, type = 'info') {
        // 如果存在全局的 showToast 函数，使用它
        if (typeof window.showToast === 'function') {
            window.showToast(message, type);
            return;
        }

        // 否则使用简单的 alert
        if (type === 'error') {
            alert(`错误: ${message}`);
        } else {
            alert(message);
        }
    }

    /**
     * 获取翻译配置
     */
    async getTranslationConfig() {
        try {
            const result = await window.api.getTranslationConfig();

            if (result.success && result.data) {
                return result.data;
            }
            return null;
        } catch (error) {
            console.error('获取翻译配置失败:', error);
            return null;
        }
    }

    /**
     * 测试翻译功能
     */
    async testTranslation() {
        try {
            const result = await window.api.testTranslation();

            return {
                success: result.success,
                message: result.message,
                data: result.data
            };
        } catch (error) {
            console.error('测试翻译功能失败:', error);
            return {
                success: false,
                message: error.message || '测试翻译功能失败',
                data: null
            };
        }
    }
}

// 创建全局翻译助手实例
window.TranslationHelper = TranslationHelper;
window.translationHelper = new TranslationHelper();
