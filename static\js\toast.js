/**
 * Toast 通知系统
 * 提供成功、错误、警告、信息等类型的通知
 */
class Toast {
    constructor() {
        this.container = null;
        this.toastCount = 0;
        this.init();
    }

    /**
     * 初始化 Toast 容器
     */
    init() {
        // 创建 Toast 容器
        this.container = document.createElement('div');
        this.container.id = 'toast-container';
        this.container.className = 'toast toast-top toast-end z-50';
        this.container.style.cssText = `
            position: fixed;
            top: 1rem;
            right: 1rem;
            z-index: 9999;
            pointer-events: none;
        `;
        
        // 添加到页面
        document.body.appendChild(this.container);
    }

    /**
     * 显示 Toast 通知
     * @param {string} message - 消息内容
     * @param {string} type - 类型 (success, error, warning, info)
     * @param {number} duration - 显示时长（毫秒）
     * @param {Object} options - 额外选项
     */
    show(message, type = 'info', duration = 4000, options = {}) {
        const toastId = `toast-${++this.toastCount}`;
        
        // 创建 Toast 元素
        const toast = document.createElement('div');
        toast.id = toastId;
        toast.className = `alert ${this.getAlertClass(type)} mb-2 shadow-lg pointer-events-auto`;
        toast.style.cssText = `
            min-width: 300px;
            max-width: 500px;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease-in-out;
            position: relative;
        `;

        // 设置内容
        toast.innerHTML = `
            <button class="btn btn-ghost btn-xs" onclick="window.toast.hide('${toastId}')" style="
                position: absolute;
                top: 0.25rem;
                right: 0.25rem;
                min-height: 1.5rem;
                height: 1.5rem;
                width: 1.5rem;
                padding: 0;
                z-index: 10;
            ">
                <i class="bi bi-x" style="font-size: 0.875rem;"></i>
            </button>
            <div class="flex items-center" style="padding-right: 2rem;">
                ${this.getIcon(type)}
                <span class="flex-1 ml-2">${this.escapeHtml(message)}</span>
            </div>
        `;

        // 添加到容器
        this.container.appendChild(toast);

        // 触发动画
        requestAnimationFrame(() => {
            toast.style.opacity = '1';
            toast.style.transform = 'translateX(0)';
        });

        // 自动隐藏
        if (duration > 0) {
            setTimeout(() => {
                this.hide(toastId);
            }, duration);
        }

        return toastId;
    }

    /**
     * 隐藏指定的 Toast
     * @param {string} toastId - Toast ID
     */
    hide(toastId) {
        const toast = document.getElementById(toastId);
        if (!toast) return;

        // 隐藏动画
        toast.style.opacity = '0';
        toast.style.transform = 'translateX(100%)';

        // 移除元素
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }

    /**
     * 清除所有 Toast
     */
    clear() {
        const toasts = this.container.querySelectorAll('[id^="toast-"]');
        toasts.forEach(toast => {
            this.hide(toast.id);
        });
    }

    /**
     * 获取对应类型的 CSS 类
     * @param {string} type - Toast 类型
     * @returns {string} CSS 类名
     */
    getAlertClass(type) {
        const classes = {
            success: 'alert-success',
            error: 'alert-error',
            warning: 'alert-warning',
            info: 'alert-info'
        };
        return classes[type] || classes.info;
    }

    /**
     * 获取对应类型的图标
     * @param {string} type - Toast 类型
     * @returns {string} 图标 HTML
     */
    getIcon(type) {
        const icons = {
            success: '<i class="bi bi-check-circle-fill text-success"></i>',
            error: '<i class="bi bi-exclamation-triangle-fill text-error"></i>',
            warning: '<i class="bi bi-exclamation-triangle-fill text-warning"></i>',
            info: '<i class="bi bi-info-circle-fill text-info"></i>'
        };
        return icons[type] || icons.info;
    }

    /**
     * HTML 转义
     * @param {string} text - 要转义的文本
     * @returns {string} 转义后的文本
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 便捷方法
    success(message, duration = 4000, options = {}) {
        return this.show(message, 'success', duration, options);
    }

    error(message, duration = 6000, options = {}) {
        return this.show(message, 'error', duration, options);
    }

    warning(message, duration = 5000, options = {}) {
        return this.show(message, 'warning', duration, options);
    }

    info(message, duration = 4000, options = {}) {
        return this.show(message, 'info', duration, options);
    }
}

// 创建全局实例
window.toast = new Toast();

// 页面加载完成后确保 Toast 容器存在
document.addEventListener('DOMContentLoaded', () => {
    if (!window.toast.container || !document.body.contains(window.toast.container)) {
        window.toast.init();
    }
});

